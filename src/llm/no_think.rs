use crate::config::app_config::{AppConfig, NoThinkMode};

const NO_THINK_PREFIX: &str = "/no_think ";
const THINK_PREFIX: &str = "/think ";

/// Strips existing `/no_think ` or `/think ` prefixes from the start of a string.
fn strip_existing_prefix(prompt_body: &str) -> String {
    if prompt_body.starts_with(NO_THINK_PREFIX) {
        prompt_body
            .strip_prefix(NO_THINK_PREFIX)
            .unwrap_or(prompt_body)
            .to_string()
    } else if prompt_body.starts_with(THINK_PREFIX) {
        prompt_body
            .strip_prefix(THINK_PREFIX)
            .unwrap_or(prompt_body)
            .to_string()
    } else {
        prompt_body.to_string()
    }
}

/// Applies the appropriate `/no_think ` or `/think ` prefix to a prompt string
/// based on the AppConfig and the specific NoThinkMode being checked.
///
/// The function modifies the `prompt_body` in place.
///
/// # Arguments
/// * `prompt_body` - A mutable reference to the String containing the prompt.
///                   Any existing `/no_think ` or `/think ` prefix will be stripped
///                   before the new one is applied.
/// * `app_config` - A reference to the application's configuration.
/// * `mode_to_check` - The specific `NoThinkMode` to evaluate for this prompt.
pub fn apply_prefix_based_on_mode(
    prompt_body: &mut String,
    app_config: &AppConfig,
    mode_to_check: NoThinkMode,
) {
    let cleaned_body = strip_existing_prefix(prompt_body);

    if app_config.no_think.contains(&NoThinkMode::None) {
        // No prefix if None is explicitly set
        *prompt_body = cleaned_body;
    } else if app_config.no_think.contains(&NoThinkMode::All) {
        // /no_think for all contexts
        *prompt_body = format!("{}{}", NO_THINK_PREFIX, cleaned_body);
    } else {
        // Selective modes: apply /no_think if mode is in set, otherwise /think
        if app_config.no_think.contains(&mode_to_check) {
            *prompt_body = format!("{}{}", NO_THINK_PREFIX, cleaned_body);
        } else {
            *prompt_body = format!("{}{}", THINK_PREFIX, cleaned_body);
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::app_config::AppConfig;
    use std::collections::HashSet;

    fn get_test_app_config(no_think_modes: HashSet<NoThinkMode>) -> AppConfig {
        // Create a minimal AppConfig for testing.
        // The key part is the `no_think` field.
        // Other fields can be default or dummy values.
        let args = crate::cli::Args {
            message: None,
            file_paths: vec![],
            // provider_api_key: None, // Removed CLI flag
            // provider_url: None, // Removed field not in Args
            default_model: None,
            results_output_mode: None, // Replaced results_output
            results_output_file: None,
            log_level: None,
            exit_on_success: false, // Replaced interactive
            no_think: None, // This is for CLI args, not directly used by AppConfig internal set
            no_timestamps: false,
            task_info: None,
            results_input_file: None, // Replaced message_history_file
            results_input: None,      // Replaced message_history
            auto_test_command: None,
            auto_test_toggle: false,
            notification_command: None,
            // Add other fields from Args as needed for AppConfig::new
            auto_research: None,
            auto_expert: None,
            expert_model: None,
            auto_expert_mode: None,
            retry_model: None,    // Added missing field
            summary_model: None,  // Added missing field
            research_model: None, // Added missing field
            decision_model: None, // Added missing field
            http_api_port: None,
        };
        let file_config = crate::config::file_config::FileConfig::default();
        let mut config = AppConfig::new(args, file_config);
        config.no_think = no_think_modes; // Directly set the HashSet for testing
        config
    }

    #[test]
    fn test_strip_existing_prefix() {
        assert_eq!(strip_existing_prefix("/no_think hello"), "hello");
        assert_eq!(strip_existing_prefix("/think world"), "world");
        assert_eq!(strip_existing_prefix("no prefix here"), "no prefix here");
        assert_eq!(
            strip_existing_prefix("/no_think /no_think multiple"),
            "/no_think multiple"
        ); // Only strips first
        assert_eq!(strip_existing_prefix(""), "");
    }

    #[test]
    fn test_apply_prefix_no_think_none() {
        let mut prompt = "my prompt".to_string();
        let mut modes = HashSet::new();
        modes.insert(NoThinkMode::None);
        let config = get_test_app_config(modes);
        apply_prefix_based_on_mode(&mut prompt, &config, NoThinkMode::EditingPlanning);
        assert_eq!(prompt, "my prompt");

        let mut prompt_with_existing = "/think my prompt".to_string();
        apply_prefix_based_on_mode(
            &mut prompt_with_existing,
            &config,
            NoThinkMode::EditingPlanning,
        );
        assert_eq!(prompt_with_existing, "my prompt");
    }

    #[test]
    fn test_apply_prefix_no_think_all() {
        let mut prompt = "my prompt".to_string();
        let mut modes = HashSet::new();
        modes.insert(NoThinkMode::All);
        let config = get_test_app_config(modes);
        apply_prefix_based_on_mode(&mut prompt, &config, NoThinkMode::EditingPlanning);
        assert_eq!(prompt, "/no_think my prompt");

        let mut prompt_with_existing = "/think my prompt".to_string();
        apply_prefix_based_on_mode(
            &mut prompt_with_existing,
            &config,
            NoThinkMode::EditingPlanning,
        );
        assert_eq!(prompt_with_existing, "/no_think my prompt");
    }

    #[test]
    fn test_apply_prefix_specific_mode_no_think() {
        let mut prompt = "my prompt".to_string();
        let mut modes = HashSet::new();
        modes.insert(NoThinkMode::EditingPlanning);
        let config = get_test_app_config(modes);
        apply_prefix_based_on_mode(&mut prompt, &config, NoThinkMode::EditingPlanning);
        assert_eq!(prompt, "/no_think my prompt");

        let mut prompt_with_existing = "/think my prompt".to_string();
        apply_prefix_based_on_mode(
            &mut prompt_with_existing,
            &config,
            NoThinkMode::EditingPlanning,
        );
        assert_eq!(prompt_with_existing, "/no_think my prompt");
    }

    #[test]
    fn test_apply_prefix_specific_mode_think() {
        let mut prompt = "my prompt".to_string();
        let modes = HashSet::new(); // No specific no_think modes set for EditingPlanning
        let config = get_test_app_config(modes);
        apply_prefix_based_on_mode(&mut prompt, &config, NoThinkMode::EditingPlanning);
        assert_eq!(prompt, "/think my prompt");

        let mut prompt_with_existing_no_think = "/no_think my prompt".to_string();
        apply_prefix_based_on_mode(
            &mut prompt_with_existing_no_think,
            &config,
            NoThinkMode::EditingPlanning,
        );
        assert_eq!(prompt_with_existing_no_think, "/think my prompt");

        let mut prompt_with_existing_think = "/think my prompt".to_string();
        apply_prefix_based_on_mode(
            &mut prompt_with_existing_think,
            &config,
            NoThinkMode::EditingPlanning,
        );
        assert_eq!(prompt_with_existing_think, "/think my prompt"); // Should strip and re-add
    }

    #[test]
    fn test_apply_prefix_ask_mode_think() {
        let mut prompt = "my question".to_string();
        let modes = HashSet::new(); // No specific no_think modes set for Ask
        let config = get_test_app_config(modes);
        apply_prefix_based_on_mode(&mut prompt, &config, NoThinkMode::Ask);
        assert_eq!(prompt, "/think my question");
    }

    #[test]
    fn test_apply_prefix_ask_mode_no_think() {
        let mut prompt = "my question".to_string();
        let mut modes = HashSet::new();
        modes.insert(NoThinkMode::Ask);
        let config = get_test_app_config(modes);
        apply_prefix_based_on_mode(&mut prompt, &config, NoThinkMode::Ask);
        assert_eq!(prompt, "/no_think my question");
    }

    #[test]
    fn test_apply_prefix_empty_prompt() {
        let mut prompt = "".to_string();
        let modes = HashSet::new();
        let config = get_test_app_config(modes);
        apply_prefix_based_on_mode(&mut prompt, &config, NoThinkMode::EditingPlanning);
        assert_eq!(prompt, "/think "); // Note the trailing space
    }

    #[test]
    fn test_apply_prefix_mode_not_in_set_defaults_to_think() {
        let mut prompt = "another prompt".to_string();
        let mut modes = HashSet::new();
        modes.insert(NoThinkMode::ResearchPlanning); // A different mode is set for no_think
        let config = get_test_app_config(modes);
        // Checking EditingPlanning, which is not in the set
        apply_prefix_based_on_mode(&mut prompt, &config, NoThinkMode::EditingPlanning);
        assert_eq!(prompt, "/think another prompt");
    }
}
