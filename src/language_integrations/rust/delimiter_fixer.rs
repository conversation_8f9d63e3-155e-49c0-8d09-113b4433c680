// src/language_integrations/rust/delimiter_fixer.rs

use crate::block_parsing::processor::{
    process_llm_response_with_blocks, BlockCount, BlockExpectation,
};
use crate::block_parsing::range_replace_block::RangeReplaceBlockParser;
use crate::block_parsing::replace_blocks::ReplaceBlockParser;
use crate::block_parsing::traits::ParsableBlock;
use crate::config::AppConfig;
use crate::editor::types::ProposedEdit;
use crate::language_integrations::rust::delimiter_utils;
use crate::llm::{client as llm_client, ChatMessage, ChatRole, LLMClient, MessageType};
use log::{debug, trace, warn};
use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::Arc;

const MAX_DELIMITER_FIX_ATTEMPTS: usize = 3;

#[derive(Debug)]
pub enum DelimiterCheckOutcome {
    Success(ProposedEdit),
    Failure(ProposedEdit, String), // Original edit, error message
}

/// Formats the net unmatched delimiter stack for display in a log or prompt.
fn format_stack_for_prompt(stack: &[char]) -> String {
    if stack.is_empty() {
        "(None - perfectly balanced)".to_string()
    } else {
        stack.iter().map(|c| c.to_string()).collect::<Vec<_>>().join(" ")
    }
}

/// Checks delimiters for a proposed edit and attempts to fix imbalances using an LLM.
#[allow(clippy::too_many_arguments)]
pub async fn check_and_fix_delimiters_for_edit(
    original_code_slice: &str,
    mut proposed_edit: ProposedEdit,
    app_config: &AppConfig,
    llm_instance: Arc<dyn LLMClient>,
    historical_context: &[ChatMessage],
    file_path: &PathBuf,
) -> DelimiterCheckOutcome {
    let original_net_stack = delimiter_utils::get_net_unmatched_delimiters(original_code_slice);
    trace!(
        "Delimiter check for {}: Original code slice unmatched stack: {:?}",
        file_path.display(),
        original_net_stack
    );
    let mut current_proposed_code = proposed_edit.new_code.clone();

    for attempt in 0..=MAX_DELIMITER_FIX_ATTEMPTS {
        let proposed_net_stack =
            delimiter_utils::get_net_unmatched_delimiters(&current_proposed_code);
        trace!(
            "Delimiter check for {} (Attempt {}): Proposed code unmatched stack: {:?}",
            file_path.display(),
            attempt + 1,
            proposed_net_stack
        );

        if original_net_stack == proposed_net_stack {
            if attempt > 0 {
                debug!(
                    "Delimiter fix successful for {} after {} attempt(s).",
                    file_path.display(),
                    attempt
                );
            }
            proposed_edit.new_code = current_proposed_code;
            return DelimiterCheckOutcome::Success(proposed_edit);
        }

        let original_char_counts = delimiter_utils::count_delimiters(original_code_slice);
        let proposed_char_counts = delimiter_utils::count_delimiters(&current_proposed_code);
        let mut discrepancy_messages =
            format_discrepancies(&original_char_counts, &proposed_char_counts);

        // **NEW LOGIC**: Handle the case where counts are balanced but the stack (order) is not.
        if discrepancy_messages.is_empty() {
            let special_message = format!(
                "- The number of opening and closing delimiters for each pair is correct, but their ORDER is wrong, creating a mismatched sequence.\n  - Required sequence of unmatched delimiters: `{}`\n  - Your code's sequence: `{}`",
                format_stack_for_prompt(&original_net_stack),
                format_stack_for_prompt(&proposed_net_stack)
            );
            discrepancy_messages.push(special_message);
        }

        if attempt == MAX_DELIMITER_FIX_ATTEMPTS {
            let error_msg = format!(
                "Delimiter issues persist for {} after {} retries. Discrepancies: {}. The problematic edit will be discarded.",
                file_path.display(),
                MAX_DELIMITER_FIX_ATTEMPTS,
                discrepancy_messages.join("\n")
            );
            warn!("{}", error_msg);
            return DelimiterCheckOutcome::Failure(proposed_edit, error_msg);
        }

        warn!(
            "Delimiter mismatch for {} (Attempt {}/{}). Original stack: {:?}, Proposed stack: {:?}. Discrepancies:\n{}. Retrying with LLM.",
            file_path.display(),
            attempt + 1,
            MAX_DELIMITER_FIX_ATTEMPTS,
            original_net_stack,
            proposed_net_stack,
            discrepancy_messages.join("\n")
        );

        let retry_prompt = construct_delimiter_fix_prompt(
            file_path,
            original_code_slice,
            &original_char_counts,
            &current_proposed_code,
            &discrepancy_messages,
            &proposed_edit.target,
        );
        trace!(
            "Delimiter fix retry for {}: Sending prompt:\n---\n{}",
            file_path.display(),
            retry_prompt,
        );

        let use_default_model = llm_instance.get_model_name() == app_config.model;
        let existing_messages_for_fix_attempt: Vec<ChatMessage> = if use_default_model {
            historical_context.to_vec()
        } else {
            vec![]
        };

        match llm_client::temporal_chat_inference(
            llm_instance.as_ref(),
            &existing_messages_for_fix_attempt,
            &retry_prompt,
        )
        .await
        {
            Ok(response) => {
                trace!(
                    "Delimiter fix retry for {}: Received response:\n---\n{}\n---",
                    file_path.display(),
                    response
                );
                let mut temp_log_for_fix_parsing = Vec::new();

                let expectations = vec![
                    BlockExpectation {
                        parser: Box::new(ReplaceBlockParser),
                        expected_count: BlockCount::Optional,
                    },
                    BlockExpectation {
                        parser: Box::new(RangeReplaceBlockParser),
                        expected_count: BlockCount::Optional,
                    },
                ];

                let mut history_for_llm_call = existing_messages_for_fix_attempt.clone();
                history_for_llm_call.push(ChatMessage {
                    role: ChatRole::User,
                    content: retry_prompt.clone(),
                    message_type: MessageType::Text,
                });

                match process_llm_response_with_blocks(
                    &response,
                    &expectations,
                    &retry_prompt,
                    &[],
                    llm_instance.clone(),
                    &history_for_llm_call,
                    &mut temp_log_for_fix_parsing,
                    app_config,
                )
                .await
                {
                    Ok(processed) => {
                        let replace_blocks_vec = processed
                            .successfully_parsed_blocks
                            .get(&ReplaceBlockParser.id());
                        let range_replace_blocks_vec = processed
                            .successfully_parsed_blocks
                            .get(&RangeReplaceBlockParser.id());

                        let num_replace = replace_blocks_vec.map_or(0, |v| v.len());
                        let num_range_replace = range_replace_blocks_vec.map_or(0, |v| v.len());

                        if num_replace == 1 && num_range_replace == 0 {
                            let block = &replace_blocks_vec.unwrap()[0];
                            if let Ok(new_code_from_llm) = ReplaceBlockParser.parse_to_string(block) {
                                current_proposed_code = new_code_from_llm;
                            } else {
                                let err_msg = format!("Delimiter fix retry for {}: ReplaceBlockParser failed to parse validated block.", file_path.display());
                                warn!("{}", err_msg);
                                return DelimiterCheckOutcome::Failure(proposed_edit, err_msg);
                            }
                        } else if num_replace == 0 && num_range_replace == 1 {
                            let block = &range_replace_blocks_vec.unwrap()[0];
                            if let Ok(parsed_range_replace) = RangeReplaceBlockParser.parse_to_target_and_content(block, &[]) {
                                current_proposed_code =
                                    parsed_range_replace.replacement_content;
                            } else {
                                let err_msg = format!("Delimiter fix retry for {}: Failed to parse 'range-replace' block. Block: {:?}", file_path.display(), block.full_block_text);
                                warn!("{}", err_msg);
                                return DelimiterCheckOutcome::Failure(proposed_edit, err_msg);
                            }
                        } else {
                            let err_msg = format!(
                                "Delimiter fix retry for {}: LLM provided {} 'replace' and {} 'range-replace' blocks. Expected a single block.",
                                file_path.display(), num_replace, num_range_replace
                            );
                            warn!("{}", err_msg);
                            return DelimiterCheckOutcome::Failure(proposed_edit, err_msg);
                        }
                    }
                    Err(e) => {
                        let err_msg = format!("Delimiter fix retry for {}: Failed to process LLM response: {}", file_path.display(), e);
                        warn!("{}", err_msg);
                        return DelimiterCheckOutcome::Failure(proposed_edit, err_msg);
                    }
                }
            }
            Err(e) => {
                let err_msg = format!(
                    "Delimiter fix retry for {}: LLM call failed: {}",
                    file_path.display(),
                    e
                );
                warn!("{}", err_msg);
                return DelimiterCheckOutcome::Failure(proposed_edit, err_msg);
            }
        }
    }

    let final_error_msg = format!(
        "Delimiter issues persist for {} after all retries. The original problematic edit will be discarded.",
        file_path.display()
    );
    warn!("{}", final_error_msg);
    DelimiterCheckOutcome::Failure(proposed_edit, final_error_msg)
}

/// Generates detailed, human-readable messages explaining delimiter discrepancies based on counts.
fn format_discrepancies(
    original_counts: &HashMap<char, u32>,
    proposed_counts: &HashMap<char, u32>,
) -> Vec<String> {
    let delimiter_pairs = [('(', ')'), ('{', '}'), ('[', ']')];
    let mut error_messages: Vec<String> = Vec::new();

    for (open_char, close_char) in &delimiter_pairs {
        let orig_open = *original_counts.get(open_char).unwrap_or(&0);
        let orig_close = *original_counts.get(close_char).unwrap_or(&0);
        let prop_open = *proposed_counts.get(open_char).unwrap_or(&0);
        let prop_close = *proposed_counts.get(close_char).unwrap_or(&0);

        let orig_net_balance = orig_open as i32 - orig_close as i32;
        let prop_net_balance = prop_open as i32 - prop_close as i32;
        let char_pair_str = format!("pair '{}'/'{}'", open_char, close_char);

        if orig_net_balance == prop_net_balance {
            continue;
        }

        if orig_net_balance == 0 {
            let proposed_unmatched_count = prop_net_balance.abs() as u32;
            if prop_net_balance > 0 {
                error_messages.push(format!(
                    "- For {}: Original code was balanced. Your code introduced {} new unmatched '{}'. The code must remain balanced for this pair.",
                    char_pair_str, proposed_unmatched_count, open_char
                ));
            } else {
                error_messages.push(format!(
                    "- For {}: Original code was balanced. Your code introduced {} new unmatched '{}'. The code must remain balanced for this pair.",
                    char_pair_str, proposed_unmatched_count, close_char
                ));
            }
        } else if orig_net_balance > 0 {
            let original_unmatched_open_count = orig_net_balance.abs() as u32;
            if prop_net_balance == 0 {
                error_messages.push(format!(
                    "- For {}: Original had {} unmatched '{}'. Your code is balanced. The original net balance ({} unmatched '{}') must be preserved.",
                    char_pair_str, original_unmatched_open_count, open_char, original_unmatched_open_count, open_char
                ));
            } else if prop_net_balance > 0 {
                let proposed_unmatched_open_count = prop_net_balance.abs() as u32;
                if proposed_unmatched_open_count < original_unmatched_open_count {
                    error_messages.push(format!(
                        "- For {}: Original had {} unmatched '{}'. Your code reduced this to {}. The original net balance ({} unmatched '{}') must be preserved.",
                        char_pair_str, original_unmatched_open_count, open_char, proposed_unmatched_open_count, original_unmatched_open_count, open_char
                    ));
                } else {
                    error_messages.push(format!(
                        "- For {}: Original had {} unmatched '{}'. Your code increased this to {}. The original net balance ({} unmatched '{}') must be preserved.",
                        char_pair_str, original_unmatched_open_count, open_char, proposed_unmatched_open_count, original_unmatched_open_count, open_char
                    ));
                }
            } else {
                let proposed_unmatched_close_count = prop_net_balance.abs() as u32;
                let pronoun = if original_unmatched_open_count == 1 { "this" } else { "these" };
                error_messages.push(format!(
                    "- For {}: Original had {} unmatched '{}'. Your code removed {} and introduced {} new unmatched '{}'. The original net balance ({} unmatched '{}') must be preserved.",
                    char_pair_str, original_unmatched_open_count, open_char, pronoun, proposed_unmatched_close_count, close_char, original_unmatched_open_count, open_char
                ));
            }
        } else {
            let original_unmatched_close_count = orig_net_balance.abs() as u32;
            if prop_net_balance == 0 {
                error_messages.push(format!(
                    "- For {}: Original had {} unmatched '{}'. Your code is balanced. The original net balance ({} unmatched '{}') must be preserved.",
                    char_pair_str, original_unmatched_close_count, close_char, original_unmatched_close_count, close_char
                ));
            } else if prop_net_balance < 0 {
                let proposed_unmatched_close_count = prop_net_balance.abs() as u32;
                if proposed_unmatched_close_count < original_unmatched_close_count {
                    error_messages.push(format!(
                        "- For {}: Original had {} unmatched '{}'. Your code reduced this to {}. The original net balance ({} unmatched '{}') must be preserved.",
                        char_pair_str, original_unmatched_close_count, close_char, proposed_unmatched_close_count, original_unmatched_close_count, close_char
                    ));
                } else {
                    error_messages.push(format!(
                        "- For {}: Original had {} unmatched '{}'. Your code increased this to {}. The original net balance ({} unmatched '{}') must be preserved.",
                        char_pair_str, original_unmatched_close_count, close_char, proposed_unmatched_close_count, original_unmatched_close_count, close_char
                    ));
                }
            } else {
                let proposed_unmatched_open_count = prop_net_balance.abs() as u32;
                let pronoun = if original_unmatched_close_count == 1 { "this" } else { "these" };
                error_messages.push(format!(
                    "- For {}: Original had {} unmatched '{}'. Your code removed {} and introduced {} new unmatched '{}'. The original net balance ({} unmatched '{}') must be preserved.",
                    char_pair_str, original_unmatched_close_count, close_char, pronoun, proposed_unmatched_open_count, open_char, original_unmatched_close_count, close_char
                ));
            }
        }
    }
    error_messages
}

/// Formats the net unmatched delimiter counts for display in the prompt.
fn format_unmatched_delimiter_counts(counts: &HashMap<char, u32>) -> String {
    let mut details = Vec::new();
    let delimiter_pairs_for_formatting = [('(', ')'), ('{', '}'), ('[', ']')];

    for (open_char, close_char) in &delimiter_pairs_for_formatting {
        let open_count = *counts.get(open_char).unwrap_or(&0);
        let close_count = *counts.get(close_char).unwrap_or(&0);

        if open_count > 0 || close_count > 0 {
            let net_balance = open_count as i32 - close_count as i32;
            if net_balance > 0 {
                details.push(format!("- {} unmatched '{}'", net_balance, open_char));
            } else if net_balance < 0 {
                details.push(format!(
                    "- {} unmatched '{}'",
                    net_balance.abs(),
                    close_char
                ));
            } else {
                details.push(format!("- Balanced for '{}'/'{}'", open_char, close_char));
            }
        }
    }

    if details.is_empty() {
        "  (Original code has no unmatched delimiters).".to_string()
    } else {
        format!("  {}", details.join("\n  "))
    }
}

fn construct_delimiter_fix_prompt(
    file_path: &PathBuf,
    original_code_slice: &str,
    original_char_counts: &HashMap<char, u32>,
    problematic_llm_code: &str,
    discrepancy_messages: &[String],
    target: &crate::editor::types::EditTarget,
) -> String {
    let original_unmatched_counts_formatted =
        format_unmatched_delimiter_counts(original_char_counts);
    let discrepancy_details_formatted = discrepancy_messages.join("\n");

    format!(
        "CRITICAL: The Rust code you previously provided for file `{}` (lines {}-{}) has an incorrect delimiter balance. When editing a slice of code, it's vital that your new code maintains the same *net unmatched delimiter counts* as the original slice it replaces. This ensures that delimiters which are intentionally left open (or closed) to match with code outside the edited range are preserved.\n\n\
        Original code snippet (lines {}-{} of `{}`):\n\
        ```rust\n{}\n```\n\
        This original code snippet has the following NET UNMATCHED DELIMITER COUNTS (your new code must achieve these exact net balances for each pair):\n{}\n\n\
        Here is your previously generated code (which had delimiter problems):\n\
        ```rust\n{}\n```\n\
        This code you generated has the following delimiter issues when compared to the original's required net balances:\n{}\n\n\
        Your task now is to provide a NEW `replace` code block. This new code must replace your problematic code. \
        The new code MUST be corrected to address all the delimiter issues listed above. \
        Specifically, the new code must result in the same NET UNMATCHED DELIMITER COUNTS for each pair ('()', '{{}}', '[]') as the *original code snippet* detailed above. This is essential for resolving all delimiter-related syntax errors in the context of the larger file.\n\n\
        You can think for 1-3 sentences first, and then provide ONLY a single `replace` block with the corrected code. Do not include any other explanations or blocks.\n\
        The `replace` block should look like this:\n\
        ```replace\n// Corrected code content here\n// Ensure it's complete and fixes the delimiter issues to match original net balances\n```",
        file_path.display(),
        target.start_line,
        target.end_line,
        target.start_line,
        target.end_line,
        file_path.display(),
        original_code_slice,
        original_unmatched_counts_formatted,
        problematic_llm_code,
        discrepancy_details_formatted
    )
}