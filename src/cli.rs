use clap::Parser;
use std::path::PathBuf;

/// Interactive LLM-Powered Code Editing
///
/// This tool facilitates precise, LLM-assisted code modifications
/// across multiple files through an interactive, two-step process.
#[derive(Parser, Debug)]
#[clap(
    author,
    version,
    about,
    long_about = None,
    after_help = "Note: Environment variables can also provide provider configuration. If the same \
                 provider is specified with both environment variables and CLI flags, \
                 the CLI flags take precedence."
)]
pub struct Args {
    /// The high-level instruction for the LLM. Optional in interactive mode.
    #[clap(short = 'm', long, value_name = "MESSAGE")]
    pub message: Option<String>,

    /// Paths to the files to be processed.
    #[clap(name = "FILE_PATHS", num_args = 1..)]
    pub file_paths: Vec<PathBuf>,

    // Provider, Model, and Provider URL are now managed via models_list in config and default_model_alias.
    // API keys are sourced from model-specific entries in models_list or environment variables.
    // The global --provider-api-key CLI flag has been removed.
    /// JSON string containing results data from a previous run to load as initial state.
    #[clap(
        short = 'i',
        long = "input",
        value_name = "JSON_STRING",
        conflicts_with = "results_input_file"
    )]
    pub results_input: Option<String>,

    /// Path to a JSON file containing results data from a previous run to load as initial state.
    #[clap(
        short = 'I',
        long = "input-file",
        value_name = "FILE_PATH",
        conflicts_with = "results_input"
    )]
    pub results_input_file: Option<PathBuf>,

    /// Additional detailed information about the task to be provided at the start of the initial prompt.
    #[clap(short = 't', long, value_name = "TEXT")]
    pub task_info: Option<String>,

    /// Specify the alias of the model configuration to use (from models_list in config).
    #[clap(short = 'M', long = "default_model", value_name = "ALIAS")]
    pub default_model: Option<String>,

    /// Set the results output mode to stdout. Options: none, simple, advanced.
    /// If --results-output-file is used, mode is implicitly 'advanced' to the file.
    #[clap(short = 'o', long = "output", value_name = "MODE")]
    pub results_output_mode: Option<String>,

    /// Path to file to output JSON results (implies 'advanced' mode).
    #[clap(short = 'R', long = "results-output-file", value_name = "FILE")]
    pub results_output_file: Option<PathBuf>,

    /// Set the logging level.
    /// 1 = Limited logs.
    /// 2 = Default logging that provides a solid overview of what is happening and reads well.
    /// 3 = Extremely verbose logging, including raw LLM responses.
    #[clap(short = 'l', long, value_name = "LEVEL")]
    pub log_level: Option<u8>,

    /// Set 'no_think' modes as a space-separated list.
    /// Options: none, all, editing-plan, editing-coding, research-auto-decision, research-planning, research-searching, ask, summaries, expert-auto-decision, expert-planning, expert-editing.
    /// 'none': Overrides all other modes.
    /// 'all': Enables /no_think for all contexts (unless 'none' is present).
    /// 'editing-plan': For initial planning.
    /// 'editing-coding': For specific code generation.
    /// 'research-auto-decision': For LLM decision on auto-research.
    /// 'research-planning': For research planning.
    /// 'research-searching': For research search cycles.
    /// 'ask': For asking questions.
    /// 'summaries': For summary generation.
    /// 'expert-auto-decision': For LLM decision on expert intervention.
    /// 'expert-planning': For expert planning.
    /// 'expert-editing': For expert editing.
    /// Example: "editing-planning ask"
    #[clap(short = 'n', long, value_name = "MODES")]
    pub no_think: Option<String>, // Remains Option<String>, parsed in AppConfig

    /// Disable timestamps in logs. Timestamps are enabled by default.
    #[clap(short = 'T', long, action = clap::ArgAction::SetTrue)]
    pub no_timestamps: bool,

    /// Run in interactive TUI mode.
    /// Command to run automatically after edits are applied, if auto-test is toggled on.
    #[clap(short = 'a', long, value_name = "COMMAND_STRING")]
    pub auto_test_command: Option<String>,

    /// Toggle the auto-test feature on. If not set, auto-test is off unless enabled in config.
    #[clap(short = 'A', long, action = clap::ArgAction::SetTrue)]
    pub auto_test_toggle: bool, // This is for the boolean on/off for tests

    /// Exit after the first successful task completion (including auto-tests).
    #[clap(short = 'x', long, action = clap::ArgAction::SetTrue)]
    // Changed short flag from 'e' to 'x'
    pub exit_on_success: bool,

    /// Set the automatic research mode.
    /// Options: false, true, forced, first, first-forced-then-false, first-forced-then-true, expert-only, no-files, expert-and-no-files-only.
    /// 'false': Disable auto-research for default LLM. Expert may research if expert_model_auto_research_loop_max > 0.
    /// 'true': (Default) Default LLM decides if research is needed; mandatory if no files in context. Expert may research.
    /// 'forced': Force default LLM to perform research. Expert may research.
    /// 'first': Like 'true', but only for the first attempt by default LLM; retries behave like 'false'. Expert may research.
    /// 'first-forced-then-false': Like 'forced' for the first attempt by default LLM; retries behave like 'false'. Expert may research.
    /// 'first-forced-then-true': Like 'forced' for the first attempt by default LLM; retries behave like 'true'. Expert may research.
    /// 'expert-only': Default LLM does not perform research. Expert LLM may perform research if configured.
    /// 'no-files': Default LLM performs research only if no files are in context. Expert LLM cannot perform research.
    /// 'expert-and-no-files-only': Default LLM performs research only if no files are in context. Expert LLM can perform research.
    #[clap(long = "auto-research", value_name = "MODE")]
    pub auto_research: Option<String>,

    /// Command to execute for notifications.
    #[clap(short = 'N', long, value_name = "COMMAND_STRING")]
    pub notification_command: Option<String>,

    /// Set the automatic expert switch.
    /// Options: false, true, forced, first, first-forced-then-false, first-forced-then-true.
    /// 'false': (Default) Auto-expert is disabled.
    /// 'true': Default LLM decides if expert is needed.
    /// 'forced': Auto-expert flow is always triggered.
    /// 'first': Like 'true', but only on the first run of an editing task.
    /// 'first-forced-then-false': 'forced' on first run, 'false' on retries.
    /// 'first-forced-then-true': 'forced' on first run, 'true' on retries.
    #[clap(long = "auto-expert", value_name = "SWITCH")]
    pub auto_expert: Option<String>,

    /// Model alias (from models_list in config) to use for expert prompts.
    #[clap(long = "expert-model", value_name = "ALIAS")]
    pub expert_model: Option<String>,

    /// Mode for auto-expert.
    /// Options: planning, editing.
    /// 'planning': (Default) Expert LLM handles initial planning.
    /// 'editing': Expert LLM handles code generation (not yet implemented).
    #[clap(long = "auto-expert-mode", value_name = "MODE")]
    pub auto_expert_mode: Option<String>,

    /// Model alias (from models_list in config) to use for decision-making prompts.
    /// If "none" or "default", uses the main default model.
    #[clap(long = "decision-model", value_name = "ALIAS")]
    pub decision_model: Option<String>,

    /// Model alias to use for retrying block parsing failures.
    #[arg(long)]
    pub retry_model: Option<String>,

    /// Model alias (from models_list in config) to use for generating summaries.
    /// If "none" or "default", uses the main default model.
    #[clap(short = 'S', long = "summary-model", value_name = "ALIAS")]
    pub summary_model: Option<String>,

    /// Model alias (from models_list in config) to use for research tasks.
    /// If "none" or "default", uses the main default model.
    /// Using a different model means research starts with a fresh context.
    #[clap(long = "research-model", value_name = "ALIAS")]
    pub research_model: Option<String>,

    /// Port for the HTTP API server.
    #[clap(long, value_name = "PORT")]
    pub http_api_port: Option<u16>,
    // Decision model already added above by previous thought step.
}
