use crate::block_parsing::traits::ParsableBlock;
use crate::block_parsing::utils::RawCodeBlock;
use crate::config::AppConfig; // For process_llm_response_with_blocks signature
use crate::files::file_handler::LabeledFile; // For process_llm_response_with_blocks signature
use crate::llm::{ChatMessage, LLMClient}; // For process_llm_response_with_blocks signature
use std::collections::HashMap;
use std::path::PathBuf; // Added import for PathBuf // For ProcessedBlocksOutput

#[derive(Debug, Clone, PartialEq, Eq)]
pub enum BlockCount {
    Optional, // 0 or 1
    Exact(usize),
    #[allow(dead_code)]
    AtLeast(usize),
    #[allow(dead_code)] // This variant is not constructed by current code.
    AtMost(usize), // If 0, means block should not be present
    Any, // Any number, including 0 (e.g., for range blocks)
}

pub struct BlockExpectation {
    // Each expectation owns its parser instance via a Boxed trait object.
    // Parsers are stateless, so creating instances like Box::new(RangeBlockParser{}) is fine.
    pub parser: Box<dyn ParsableBlock>,
    pub expected_count: BlockCount,
}

pub struct ProcessedBlocksOutput {
    // Key: parser.id()
    // Value: Vec of RawCodeBlocks that this parser has successfully validated.
    // The caller will use the parser's concrete parsing method to get typed data.
    pub successfully_parsed_blocks: HashMap<String, Vec<RawCodeBlock>>,
    pub remaining_raw_blocks: Vec<RawCodeBlock>, // Blocks not claimed by any parser
                                                 // Add conversation_log if it's modified and needs to be returned
}

impl ProcessedBlocksOutput {
    // Helper method to get the first block for a given parser ID
    pub fn get_first_block_by_id(&self, parser_id: &str) -> Option<&RawCodeBlock> {
        self.successfully_parsed_blocks
            .get(parser_id)
            .and_then(|blocks| blocks.first())
    }
}

use crate::block_parsing::traits::MalformedBlockInfo;
use crate::block_parsing::utils::{extract_raw_code_blocks, strip_think_tags}; // Added strip_think_tags
use crate::config::app_config::setup_specific_llm_client; // Added import
use crate::llm::client::chat_inference; // Updated path
use crate::llm::{ChatRole, MessageType};
use log::{debug, error, trace, warn};
use std::collections::HashSet;
use std::sync::Arc; // Added import

const MAX_FIX_ATTEMPTS: usize = 3;

#[allow(clippy::too_many_arguments)]
pub async fn process_llm_response_with_blocks(
    initial_llm_response_text: &str,
    expectations: &[BlockExpectation],
    original_user_prompt: &str,
    available_files: &[LabeledFile],
    llm_instance: Arc<dyn LLMClient>,             // Changed to Arc
    history_prefix_for_llm_calls: &[ChatMessage], // New: The history *before* the current `conversation_log` started
    conversation_log: &mut Vec<ChatMessage>, // The evolving log for this specific block processing
    app_config: &AppConfig,                  // Renamed from _app_config to use it
) -> Result<ProcessedBlocksOutput, String> {
    // Add the initial assistant response (raw) to the conversation log for this processing attempt.
    // This ensures that if a retry occurs, the "failing assistant message" is correctly part of the log.
    if conversation_log.is_empty()
        || conversation_log.last().map_or(false, |msg| {
            msg.role != ChatRole::Assistant || msg.content != initial_llm_response_text
            // Use raw initial_llm_response_text for comparison
        })
    {
        conversation_log.push(ChatMessage {
            role: ChatRole::Assistant,
            content: initial_llm_response_text.to_string(), // Log the raw response
            message_type: MessageType::Text,
        });
    }

    // Strip think tags for block processing.
    // current_llm_response_text will hold the version of the response used for block extraction.
    let mut current_llm_response_text = strip_think_tags(initial_llm_response_text);

    let mut fix_attempts = 0;
    let mut current_closed_blocks: Vec<RawCodeBlock>; // Declare here

    let mut last_attempt_successfully_parsed_blocks: HashMap<String, Vec<RawCodeBlock>> =
        HashMap::new();
    let mut last_attempt_claimed_indices: HashSet<usize> = HashSet::new();
    let mut last_attempt_malformed_info: Vec<MalformedBlockInfo> = Vec::new();
    let mut last_attempt_count_mismatch_details: Vec<String> = Vec::new();
    let mut last_attempt_duplicate_range_replace_details: Vec<String> = Vec::new(); // New
                                                                                    // This will store MalformedBlockInfo for an unclosed block, if detected.
    let mut unclosed_block_issue_for_this_attempt: Option<MalformedBlockInfo>;

    loop {
        let extracted_block_data = extract_raw_code_blocks(&current_llm_response_text);
        current_closed_blocks = extracted_block_data.closed_blocks; // Assign here
        unclosed_block_issue_for_this_attempt = None; // Reset for current attempt

        last_attempt_malformed_info.clear();
        last_attempt_count_mismatch_details.clear();
        last_attempt_duplicate_range_replace_details.clear(); // New: Clear for this attempt
        last_attempt_successfully_parsed_blocks.clear(); // Final successful blocks for this attempt
        last_attempt_claimed_indices.clear(); // Indices claimed by any means (success or malformed)

        // Check for an unclosed block first (same as before)
        if let Some(unclosed_block) = &extracted_block_data.unclosed_block_at_end {
            let mut is_problematic_unclosed_block = true;
            if !unclosed_block.keyword.is_empty() {
                let expected_keywords: HashSet<&str> = expectations
                    .iter()
                    .map(|exp| exp.parser.keyword())
                    .collect();
                if !expected_keywords.contains(unclosed_block.keyword.as_str()) {
                    is_problematic_unclosed_block = false;
                    trace!("Ignoring unclosed block with unexpected keyword '{}'. Expected keywords: {:?}", unclosed_block.keyword, expected_keywords);
                }
            }
            if is_problematic_unclosed_block {
                let suggestion = format!(
                    "The block starting with '```{}' was not properly closed with '```'. The content captured until the end of the response was: '{}'. Please ensure all code blocks are correctly terminated.",
                    unclosed_block.keyword,
                    unclosed_block.content_after_keyword.chars().take(200).collect::<String>()
                );
                let malformed_info = MalformedBlockInfo {
                    raw_text: unclosed_block.full_block_text.clone(),
                    fix_suggestion: suggestion,
                    parser_id: "unclosed_block_detector".to_string(),
                };
                last_attempt_malformed_info.push(malformed_info.clone());
                unclosed_block_issue_for_this_attempt = Some(malformed_info);
            }
        }

        // Pass 1: Tentative validation and malformed identification
        let mut potential_success_per_parser: HashMap<String, Vec<(usize, RawCodeBlock)>> =
            HashMap::new();

        for (idx, raw_block) in current_closed_blocks.iter().enumerate() {
            let mut claimed_this_block_iteration = false;
            // Check if already claimed as malformed from unclosed_block_detector (though unclosed shouldn't be in closed_blocks)
            // or by a previous parser in this loop (e.g. if multiple parsers could identify it as malformed).
            if last_attempt_claimed_indices.contains(&idx) {
                continue;
            }

            for expectation in expectations {
                let parser = &expectation.parser;
                if raw_block.keyword == parser.keyword() {
                    match parser.validate_raw_block(raw_block, available_files) {
                        Ok(_) => {
                            potential_success_per_parser
                                .entry(parser.id())
                                .or_default()
                                .push((idx, raw_block.clone()));
                            // Don't mark as claimed by success yet.
                            claimed_this_block_iteration = true;
                            break;
                        }
                        Err(_) => {
                            if let Some(info) =
                                parser.identify_malformed(raw_block, available_files)
                            {
                                last_attempt_malformed_info.push(info);
                                last_attempt_claimed_indices.insert(idx); // Claimed as malformed
                                claimed_this_block_iteration = true;
                                break;
                            }
                        }
                    }
                }
            }

            if !claimed_this_block_iteration {
                // If not handled by a keyword-matching parser
                for expectation in expectations {
                    // Try identify_malformed with all parsers
                    let parser = &expectation.parser;
                    if raw_block.keyword != parser.keyword() {
                        // Avoid re-evaluating with the same parser if keyword matched but validate failed
                        if let Some(info) = parser.identify_malformed(raw_block, available_files) {
                            last_attempt_malformed_info.push(info);
                            last_attempt_claimed_indices.insert(idx); // Claimed as malformed
                            break;
                        }
                    }
                }
            }
        }

        // Pass 2: Count checks and finalizing successful blocks
        let mut found_issues_for_retry = unclosed_block_issue_for_this_attempt.is_some()
            || !last_attempt_malformed_info.is_empty();

        for expectation in expectations {
            let parser = &expectation.parser;
            let mut actual_blocks_for_parser_with_indices = potential_success_per_parser
                .get(&parser.id())
                .cloned()
                .unwrap_or_default();

            // --- New: Check for duplicate range-replace targets ---
            if parser.id()
                == crate::block_parsing::range_replace_block::RangeReplaceBlockParser.id()
                && !actual_blocks_for_parser_with_indices.is_empty()
            {
                let mut edit_targets_seen: HashMap<(PathBuf, usize, usize), Vec<usize>> =
                    HashMap::new();
                let mut indices_of_duplicated_blocks: HashSet<usize> = HashSet::new();
                let current_dir_for_norm = std::env::current_dir().unwrap_or_default();

                for (original_idx, raw_block) in &actual_blocks_for_parser_with_indices {
                    if let Ok(parsed_rr) =
                        crate::block_parsing::range_replace_block::RangeReplaceBlockParser
                            .parse_to_target_and_content(raw_block, available_files)
                    {
                        let mut path_key = parsed_rr.edit_target.file_path.clone();
                        if !path_key.is_absolute() {
                            path_key = current_dir_for_norm.join(&path_key);
                        }
                        if parsed_rr.edit_target.start_line != 0
                            || parsed_rr.edit_target.end_line != 0
                        {
                            // Only canonicalize existing file paths
                            if let Ok(canon_path) = dunce::canonicalize(&path_key) {
                                path_key = canon_path;
                            } else {
                                // If canonicalization fails, use the resolved path; validation errors will be caught later if it's truly invalid.
                                // For duplicate check, consistency is key.
                            }
                        }
                        let target_key = (
                            path_key,
                            parsed_rr.edit_target.start_line,
                            parsed_rr.edit_target.end_line,
                        );
                        edit_targets_seen
                            .entry(target_key)
                            .or_default()
                            .push(*original_idx);
                    }
                }

                for (target_key, block_indices) in edit_targets_seen {
                    if block_indices.len() > 1 {
                        found_issues_for_retry = true;
                        let detail = format!(
                            "Multiple `range-replace` blocks target the same file '{}' and range {}-{}. Only one block per target is allowed.",
                            target_key.0.display(), target_key.1, target_key.2
                        );
                        trace!("{}", detail);
                        last_attempt_duplicate_range_replace_details.push(detail);
                        for idx in block_indices {
                            indices_of_duplicated_blocks.insert(idx);
                        }
                    }
                }

                if !indices_of_duplicated_blocks.is_empty() {
                    // Filter out the duplicated blocks from being considered successful for this parser
                    actual_blocks_for_parser_with_indices
                        .retain(|(idx, _)| !indices_of_duplicated_blocks.contains(idx));
                    // Also mark these original indices as "claimed" (as problematic) so they don't become "remaining_raw_blocks" without reason.
                    for idx in indices_of_duplicated_blocks {
                        last_attempt_claimed_indices.insert(idx);
                    }
                }
            }
            // --- End duplicate check ---

            let actual_count = actual_blocks_for_parser_with_indices.len();

            let count_ok = match expectation.expected_count {
                BlockCount::Optional => actual_count <= 1,
                BlockCount::Exact(n) => actual_count == n,
                BlockCount::AtLeast(n) => actual_count >= n,
                BlockCount::AtMost(n) => actual_count <= n,
                BlockCount::Any => true,
            };

            if count_ok {
                if !actual_blocks_for_parser_with_indices.is_empty() {
                    let blocks_to_store: Vec<RawCodeBlock> = actual_blocks_for_parser_with_indices
                        .iter()
                        .map(|(_, block)| block.clone())
                        .collect();
                    last_attempt_successfully_parsed_blocks.insert(parser.id(), blocks_to_store);
                    for (idx, _) in actual_blocks_for_parser_with_indices {
                        last_attempt_claimed_indices.insert(idx); // Claimed by successful expectation
                    }
                }
            } else {
                found_issues_for_retry = true;
                let detail = match expectation.expected_count {
                    BlockCount::Optional if actual_count > 1 => format!(
                        "For '{}' blocks, expected at most one (0 or 1) block, but found {}. Please provide only one or none. (Format: {})",
                        parser.id(),
                        actual_count,
                        parser.block_format_description()
                    ),
                    _ => format!(
                        "For '{}' blocks (format: {}), expected {:?} blocks, but found {}.",
                        parser.id(),
                        parser.block_format_description(),
                        expectation.expected_count,
                        actual_count
                    ),
                };
                trace!("{}", detail);
                last_attempt_count_mismatch_details.push(detail);
                // Indices from actual_blocks_for_parser_with_indices are NOT added to last_attempt_claimed_indices here,
                // so they will become remaining_raw_blocks if not claimed as malformed.
            }
        }

        // Decision (remains the same structure)
        if !found_issues_for_retry {
            trace!(
                "All blocks parsed and validated successfully in attempt {}.",
                fix_attempts + 1
            );
            let remaining_raw_blocks = current_closed_blocks
                .iter()
                .enumerate()
                .filter(|(idx, _)| !last_attempt_claimed_indices.contains(idx))
                .map(|(_, block)| block.clone())
                .collect();
            return Ok(ProcessedBlocksOutput {
                successfully_parsed_blocks: last_attempt_successfully_parsed_blocks,
                remaining_raw_blocks,
            });
        }

        fix_attempts += 1;
        if fix_attempts > MAX_FIX_ATTEMPTS {
            // Max attempts reached, break loop. Warning and decision to proceed will be handled after the loop.
            break;
        }

        debug!(
            "Attempt {}/{} to fix block parsing issues. Issues found: {} malformed (including potential unclosed), {} count mismatches, {} duplicate range-replace targets.",
            fix_attempts, MAX_FIX_ATTEMPTS, last_attempt_malformed_info.len(), last_attempt_count_mismatch_details.len(), last_attempt_duplicate_range_replace_details.len()
        );

        // Construct retry prompt
        let mut retry_prompt_content = String::from("There were issues with the blocks you provided in your previous response. Please review the feedback and provide a corrected full response containing all necessary blocks.\n\n");

        let mut malformed_by_parser: HashMap<String, Vec<MalformedBlockInfo>> = HashMap::new();
        // Iterate over a clone or collect first, to avoid borrow checker issues if last_attempt_malformed_info is modified.
        // However, we are draining it in the original code, so let's stick to that pattern if it's intended.
        // For clarity, let's filter out the unclosed_block_detector info before grouping by parser.
        for info in last_attempt_malformed_info
            .iter()
            .filter(|i| i.parser_id != "unclosed_block_detector")
        {
            malformed_by_parser
                .entry(info.parser_id.clone())
                .or_default()
                .push(info.clone());
        }

        for expectation in expectations {
            let parser = &expectation.parser;
            if let Some(infos) = malformed_by_parser.get(&parser.id()) {
                if let Some(segment) = parser.construct_fixer_prompt_segment(infos, available_files)
                // infos is already a slice here
                {
                    retry_prompt_content.push_str(&segment);
                    retry_prompt_content.push_str("\n");
                }
            }
        }

        // Specifically add feedback for the unclosed block, if present
        if let Some(unclosed_info) = &unclosed_block_issue_for_this_attempt {
            retry_prompt_content
                .push_str("\nAdditionally, a critical block formatting error was detected:\n");
            retry_prompt_content.push_str(&format!("- {}\n", unclosed_info.fix_suggestion));
            retry_prompt_content.push_str("  The problematic unclosed block segment was:\n");
            retry_prompt_content.push_str("  ```text\n");
            // Ensure raw_text is escaped if it could contain ```
            retry_prompt_content.push_str(&unclosed_info.raw_text.replace("```", "'''"));
            retry_prompt_content.push_str("\n  ```\n\n");
        }

        if !last_attempt_count_mismatch_details.is_empty() {
            retry_prompt_content
                .push_str("Additionally, there were issues with the number of blocks provided:\n");
            for detail in &last_attempt_count_mismatch_details {
                retry_prompt_content.push_str(&format!("- {}\n", detail));
            }
            retry_prompt_content.push_str("\n");
        }

        if !last_attempt_duplicate_range_replace_details.is_empty() {
            retry_prompt_content.push_str("Additionally, you provided multiple `range-replace` blocks for the same file/range. This is not allowed. Consolidate changes for each unique target into a single block. If the original blocks had conflicting or sequential changes for the same range, synthesize them into one coherent update. Details:\n");
            for detail in &last_attempt_duplicate_range_replace_details {
                retry_prompt_content.push_str(&format!("- {}\n", detail));
            }
            retry_prompt_content.push_str("\n");
        }

        retry_prompt_content.push_str("Please ensure your entire response strictly adheres to the required block structure and counts. The expected block formats are:\n");
        for expectation in expectations {
            retry_prompt_content.push_str(&format!(
                "- {}: {}\n",
                expectation.parser.keyword(),
                expectation.parser.block_format_description()
            ));
        }
        retry_prompt_content.push_str(&format!(
            "\nThe original user request was: \"{}\"\n",
            original_user_prompt
        ));

        let use_special_retry_model_for_file_listing = app_config // Determine if files should be listed
            .retry_model
            .as_ref()
            .map_or(false, |alias| {
                alias != "none" && alias != "default" && alias != &app_config.default_model
            });

        if use_special_retry_model_for_file_listing && !available_files.is_empty() {
            retry_prompt_content.push_str("The available files are (please use these exact paths if referring to an existing file):\n");
            for lf in available_files {
                let display_path = lf
                    .path
                    .strip_prefix(std::env::current_dir().unwrap_or_default())
                    .unwrap_or(&lf.path)
                    .display();
                retry_prompt_content.push_str(&format!("- {}\n", display_path));
            }
        }

        let fixer_user_message = ChatMessage {
            role: ChatRole::User,
            content: retry_prompt_content,
            message_type: MessageType::Text,
        };

        // Add user's fixer prompt to conversation log *before* deciding context for LLM call
        conversation_log.push(fixer_user_message.clone());

        let llm_for_retry_attempt: Arc<dyn LLMClient>; // Removed mut
        let context_for_retry_attempt: Vec<ChatMessage>;

        let use_special_retry_model = app_config
            .retry_model
            .as_ref()
            .map_or(false, |alias| alias != "none" && alias != "default");

        if use_special_retry_model {
            let retry_alias = app_config.retry_model.as_ref().unwrap();
            // Compare with the main default model alias.
            if retry_alias != &app_config.default_model {
                debug!(
                    "Using special retry model ('{}') with minimal context.",
                    retry_alias
                );
                llm_for_retry_attempt = setup_specific_llm_client(app_config, retry_alias)
                    .map_err(|e| format!("Failed to setup retry LLM ('{}'): {}", retry_alias, e))?;

                // Minimal context: original user prompt, failing assistant response, fixer user message.
                let fixer_message = conversation_log.last().unwrap().clone(); // Safe, as fixer_user_message was just pushed.
                if conversation_log.len() >= 2 {
                    // We have at least fixer_message and the one before it (failing_assistant_msg)
                    let failing_assistant_message =
                        conversation_log[conversation_log.len() - 2].clone();
                    context_for_retry_attempt = vec![
                        ChatMessage {
                            role: ChatRole::User,
                            content: original_user_prompt.to_string(),
                            message_type: MessageType::Text,
                        },
                        failing_assistant_message,
                        fixer_message,
                    ];
                } else {
                    // conversation_log.len() must be 1 (only contains fixer_message).
                    // This implies the initial conversation_log passed to process_llm_response_with_blocks was empty.
                    warn!("Building minimal retry context: Initial conversation_log was too short to include the previous assistant message. Using original prompt and current fixer message only.");
                    context_for_retry_attempt = vec![
                        ChatMessage {
                            role: ChatRole::User,
                            content: original_user_prompt.to_string(),
                            message_type: MessageType::Text,
                        },
                        fixer_message,
                    ];
                }
            } else {
                debug!(
                    "Retry model alias ('{}') is same as default model. Using full context.",
                    retry_alias
                );
                // Use the specified retry model (which is the default model), but with full context.
                llm_for_retry_attempt = setup_specific_llm_client(app_config, retry_alias)
                    .map_err(|e| format!("Failed to setup retry LLM ('{}'): {}", retry_alias, e))?;

                let mut full_context = history_prefix_for_llm_calls.to_vec();
                full_context.extend_from_slice(conversation_log);
                context_for_retry_attempt = full_context;
            }
        } else {
            debug!("No special retry model specified or set to 'none'/'default'. Using original LLM with full context.");
            llm_for_retry_attempt = llm_instance.clone(); // Clone the Arc of the original LLM

            let mut full_context = history_prefix_for_llm_calls.to_vec();
            full_context.extend_from_slice(conversation_log);
            context_for_retry_attempt = full_context;
        }

        match chat_inference(llm_for_retry_attempt.as_ref(), &context_for_retry_attempt).await {
            Ok(fixed_response_raw) => {
                // The assistant's fixed response (raw) is added to the main conversation_log *after* the call.
                let assistant_fixed_msg = ChatMessage {
                    role: ChatRole::Assistant,
                    content: fixed_response_raw.clone(), // Log the raw response
                    message_type: MessageType::Text,
                };
                conversation_log.push(assistant_fixed_msg);

                // Strip think tags from the new response before the next parsing attempt.
                current_llm_response_text = strip_think_tags(&fixed_response_raw);
                trace!(
                    "LLM response (raw) after fix attempt {}:\n{}",
                    fix_attempts,
                    fixed_response_raw
                );
                if fixed_response_raw != current_llm_response_text {
                    trace!(
                        "LLM response (stripped for block parsing) after fix attempt {}:\n{}",
                        fix_attempts,
                        current_llm_response_text
                    );
                }
            }
            Err(e) => {
                error!("Error sending fixer prompt to LLM: {}", e);
                return Err(format!("Error during block fixing LLM call: {}", e));
            }
        }
    } // End of loop

    // If loop finished, it means MAX_FIX_ATTEMPTS was reached with issues remaining
    // (or an LLM call failed, in which case an Err would have been returned from within the loop).
    // Log the remaining issues from the last attempt and proceed with successfully parsed blocks.

    let mut error_messages_for_log = Vec::new();
    // Use the state of unclosed_block_issue_for_this_attempt, last_attempt_malformed_info,
    // last_attempt_count_mismatch_details, and last_attempt_duplicate_range_replace_details
    // from the *final* iteration of the loop.

    if let Some(info) = &unclosed_block_issue_for_this_attempt {
        if error_messages_for_log.is_empty() {
            error_messages_for_log.push("Remaining issues from last attempt:".to_string());
        }
        error_messages_for_log.push(format!(
            "  - Unclosed Block (Original Keyword Hint: '{}'): '{}'. Suggestion: '{}'",
            info.raw_text
                .lines()
                .next()
                .unwrap_or("")
                .split_whitespace()
                .nth(0)
                .unwrap_or("N/A")
                .replace("```", ""),
            info.raw_text.chars().take(100).collect::<String>(),
            info.fix_suggestion
        ));
    }

    let other_malformed_blocks: Vec<&MalformedBlockInfo> = last_attempt_malformed_info
        .iter()
        .filter(|i| i.parser_id != "unclosed_block_detector")
        .collect();
    if !other_malformed_blocks.is_empty() {
        if error_messages_for_log.is_empty() {
            error_messages_for_log.push("Remaining issues from last attempt:".to_string());
        }
        error_messages_for_log.push("  Malformed blocks:".to_string());
        for info in other_malformed_blocks {
            error_messages_for_log.push(format!(
                "    - Parser '{}', Block: '{}', Suggestion: '{}'",
                info.parser_id,
                info.raw_text.chars().take(100).collect::<String>(),
                info.fix_suggestion
            ));
        }
    }

    if !last_attempt_count_mismatch_details.is_empty() {
        if error_messages_for_log.is_empty() {
            error_messages_for_log.push("Remaining issues from last attempt:".to_string());
        }
        error_messages_for_log.push("  Block count mismatches:".to_string());
        for detail in &last_attempt_count_mismatch_details {
            error_messages_for_log.push(format!("    - {}", detail));
        }
    }

    if !last_attempt_duplicate_range_replace_details.is_empty() {
        if error_messages_for_log.is_empty() {
            error_messages_for_log.push("Remaining issues from last attempt:".to_string());
        }
        error_messages_for_log.push("  Duplicate range-replace targets:".to_string());
        for detail in &last_attempt_duplicate_range_replace_details {
            error_messages_for_log.push(format!("    - {}", detail));
        }
    }

    if !error_messages_for_log.is_empty() {
        warn!(
            "Max fix attempts ({}) reached. Proceeding with successfully parsed blocks from the last attempt. {}",
            MAX_FIX_ATTEMPTS,
            error_messages_for_log.join("\n")
        );
    } else if fix_attempts > MAX_FIX_ATTEMPTS {
        // Only log this if we actually hit max attempts
        // This case implies found_issues_for_retry was true on the last iteration, but no specific errors were formatted.
        // This might happen if an unclosed block was the *only* issue and it was somehow resolved or ignored in the last pass
        // (which shouldn't happen with current logic, but good to have a fallback message).
        warn!(
            "Max fix attempts ({}) reached. Proceeding with successfully parsed blocks from the last attempt. No specific remaining issues were logged for the final attempt, but issues were detected.",
            MAX_FIX_ATTEMPTS
        );
    }
    // If fix_attempts <= MAX_FIX_ATTEMPTS, it means the loop exited due to success, so no warning needed here.

    // Proceed with successfully parsed blocks from the last attempt.
    // current_closed_blocks are from the *last* LLM response.
    // last_attempt_claimed_indices are from the *last* parsing attempt on those blocks.
    let remaining_raw_blocks = current_closed_blocks
        .iter()
        .enumerate()
        .filter(|(idx, _)| !last_attempt_claimed_indices.contains(idx))
        .map(|(_, block)| block.clone())
        .collect();

    Ok(ProcessedBlocksOutput {
        successfully_parsed_blocks: last_attempt_successfully_parsed_blocks, // From *last* parsing attempt
        remaining_raw_blocks,
    })
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::block_parsing::range_blocks::RangeBlockParser;
    // use crate::block_parsing::utils::ExtractedBlocks; // Removed unused import
    use crate::block_parsing::replace_blocks::ReplaceBlockParser;
    use crate::block_parsing::summary_blocks::SummaryBlockParser;
    use crate::config::AppConfig;
    // use crate::config::app_config::NoThinkMode; // Removed unused import
    use crate::llm::{ChatMessage, LLMClient};
    use async_trait::async_trait;
    use std::collections::HashSet;
    use std::io::Write;
    use std::sync::Mutex;
    use tempfile::NamedTempFile;

    // --- Mock LLMClient (remains the same) ---
    struct MockLLMClient {
        responses: Mutex<Vec<String>>,
    }

    impl MockLLMClient {
        fn new(responses: Vec<String>) -> Self {
            MockLLMClient {
                responses: Mutex::new(responses.into_iter().rev().collect()),
            }
        }
    }

    #[async_trait]
    impl LLMClient for MockLLMClient {
        async fn chat(
            &self,
            _messages: &[ChatMessage],
        ) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
            let mut responses_guard = self.responses.lock().unwrap();
            if let Some(response) = responses_guard.pop() {
                Ok(response)
            } else {
                Err("MockLLMClient: No more responses configured.".into())
            }
        }

        fn get_model_name(&self) -> &str {
            "mock-model"
        }
    }

    // --- Helper Functions (remains the same) ---
    fn get_default_app_config() -> AppConfig {
        // Create a minimal AppConfig for testing
        // Assuming AppConfig::new and related structs are accessible and can be default-constructed or minimally set up.
        // This might need adjustment based on actual AppConfig structure and its dependencies.
        let args = crate::cli::Args {
            message: Some("test prompt".to_string()),
            file_paths: vec![],
            // provider_api_key: None, // Removed field
            // provider_url: None, // Removed field not in Args
            default_model: None,       // Added default_model
            results_output_mode: None, // Replaced results_output
            results_output_file: None,
            log_level: None,
            exit_on_success: false, // Replaced interactive
            no_think: None,         // This will be a string for CLI args
            no_timestamps: false,
            task_info: None,
            results_input_file: None,   // Replaced message_history_file
            results_input: None,        // Replaced message_history
            auto_test_command: None,    // Added
            auto_test_toggle: false,    // Added
            notification_command: None, // Added
            auto_research: None,        // Added missing field
            auto_expert: None,          // Added missing field
            expert_model: None,         // Added missing field
            auto_expert_mode: None,     // Added missing field
            retry_model: None,          // Added missing field
            research_model: None,       // Added missing field for test
            summary_model: None,        // Added missing field for test
            decision_model: None,       // Added missing field
            http_api_port: None,
        };
        let mut file_config = crate::config::file_config::FileConfig::default();
        // Ensure no_think in file_config is also None for a clean default test state
        file_config.no_think = None;

        let mut app_config = AppConfig::new(args, file_config);
        // Explicitly set no_think to an empty HashSet for these tests,
        // as the default parsing might pick up modes if args.no_think was Some("")
        app_config.no_think = HashSet::new();
        app_config
    }

    fn create_temp_file(content: &str) -> (NamedTempFile, LabeledFile) {
        let temp_file = NamedTempFile::new().unwrap();
        write!(temp_file.as_file(), "{}", content).unwrap();
        let labeled_file = LabeledFile::new(temp_file.path().to_path_buf(), content);
        (temp_file, labeled_file)
    }

    // --- Test Cases ---

    #[tokio::test]
    async fn test_process_all_blocks_correct_first_try() {
        let (_temp_file, labeled_file) = create_temp_file("line1\nline2\n");
        let available_files = vec![labeled_file.clone()];
        let app_config = get_default_app_config();
        let history_prefix_for_llm_calls: &[ChatMessage] = &[];
        let llm_client = MockLLMClient::new(vec![]); // No retries needed

        let initial_response = format!(
            "Some thoughts...\n```summary\nThis is a plan.\n```\n```range\n{}\n1-1\n```\n```replace\nnew content\n```",
            labeled_file.path.display()
        );

        let expectations = vec![
            BlockExpectation {
                parser: Box::new(SummaryBlockParser),
                expected_count: BlockCount::Exact(1),
            },
            BlockExpectation {
                parser: Box::new(RangeBlockParser),
                expected_count: BlockCount::Exact(1),
            },
            BlockExpectation {
                parser: Box::new(ReplaceBlockParser),
                expected_count: BlockCount::Exact(1),
            },
        ];
        let mut conversation_log = Vec::new();

        let result = process_llm_response_with_blocks(
            &initial_response,
            &expectations,
            "test prompt",
            &available_files,
            Arc::new(llm_client),         // Wrap MockLLMClient in Arc for tests
            history_prefix_for_llm_calls, // Added missing argument
            &mut conversation_log,
            &app_config, // Pass by reference
        )
        .await;

        assert!(result.is_ok(), "Processing failed: {:?}", result.err());
        let output = result.unwrap();
        assert_eq!(
            output
                .successfully_parsed_blocks
                .get(&SummaryBlockParser.id())
                .map_or(0, |v| v.len()),
            1
        );
        assert_eq!(
            output
                .successfully_parsed_blocks
                .get(&RangeBlockParser.id())
                .map_or(0, |v| v.len()),
            1
        );
        assert_eq!(
            output
                .successfully_parsed_blocks
                .get(&ReplaceBlockParser.id())
                .map_or(0, |v| v.len()),
            1
        );
        assert!(output.remaining_raw_blocks.is_empty());
    }

    #[tokio::test]
    async fn test_process_malformed_block_fixed_after_retry() {
        let (_temp_file, labeled_file) = create_temp_file("line1\nline2\n");
        let available_files = vec![labeled_file.clone()];
        let app_config = get_default_app_config();
        let history_prefix_for_llm_calls: &[ChatMessage] = &[];

        let initial_response = format!(
            "```range\n{}\n1-abc\n```", // Malformed range
            labeled_file.path.display()
        );
        let corrected_response = format!(
            "```range\n{}\n1-1\n```", // Corrected range
            labeled_file.path.display()
        );
        let llm_client = MockLLMClient::new(vec![corrected_response.to_string()]);

        let expectations = vec![BlockExpectation {
            parser: Box::new(RangeBlockParser),
            expected_count: BlockCount::Exact(1),
        }];
        let mut conversation_log = Vec::new();

        let result = process_llm_response_with_blocks(
            &initial_response,
            &expectations,
            "test prompt",
            &available_files,
            Arc::new(llm_client),         // Wrap MockLLMClient in Arc for tests
            history_prefix_for_llm_calls, // Added missing argument
            &mut conversation_log,
            &app_config, // Pass by reference
        )
        .await;

        assert!(result.is_ok(), "Processing failed: {:?}", result.err());
        let output = result.unwrap();
        assert_eq!(
            output
                .successfully_parsed_blocks
                .get(&RangeBlockParser.id())
                .map_or(0, |v| v.len()),
            1
        );
        assert_eq!(conversation_log.len(), 3); // Initial Assistant Msg + User fixer prompt + Assistant fixed response
    }

    #[tokio::test]
    async fn test_process_incorrect_count_fixed_after_retry() {
        let app_config = get_default_app_config();
        let history_prefix_for_llm_calls: &[ChatMessage] = &[];
        let initial_response = "```summary\nSummary 1\n```\n```summary\nSummary 2\n```"; // Too many
        let corrected_response = "```summary\nCorrected Summary\n```"; // Correct count
        let llm_client = MockLLMClient::new(vec![corrected_response.to_string()]);

        let expectations = vec![BlockExpectation {
            parser: Box::new(SummaryBlockParser),
            expected_count: BlockCount::Exact(1),
        }];
        let mut conversation_log = Vec::new();

        let result = process_llm_response_with_blocks(
            &initial_response,
            &expectations,
            "test prompt",
            &[],                          // No files needed for summary
            Arc::new(llm_client),         // Wrap MockLLMClient in Arc for tests
            history_prefix_for_llm_calls, // Added missing argument
            &mut conversation_log,
            &app_config, // Pass by reference
        )
        .await;

        assert!(result.is_ok(), "Processing failed: {:?}", result.err());
        let output = result.unwrap();
        assert_eq!(
            output
                .successfully_parsed_blocks
                .get(&SummaryBlockParser.id())
                .map_or(0, |v| v.len()),
            1
        );
        assert_eq!(conversation_log.len(), 3); // Initial Assistant + User fixer prompt + Assistant fixed response
    }

    #[tokio::test]
    async fn test_process_issues_persist_after_max_retries() {
        let app_config = get_default_app_config();
        let history_prefix_for_llm_calls: &[ChatMessage] = &[];
        // LLM keeps returning the same malformed response
        let malformed_response = "```summary\nSummary 1\n```\n```summary\nSummary 2\n```";
        let llm_client = MockLLMClient::new(vec![
            malformed_response.to_string(),
            malformed_response.to_string(),
            malformed_response.to_string(),
        ]);

        let expectations = vec![BlockExpectation {
            parser: Box::new(SummaryBlockParser),
            expected_count: BlockCount::Exact(1),
        }];
        let mut conversation_log = Vec::new();

        let result = process_llm_response_with_blocks(
            malformed_response,
            &expectations,
            "test prompt",
            &[],
            Arc::new(llm_client),         // Wrap MockLLMClient in Arc for tests
            history_prefix_for_llm_calls, // Added missing argument
            &mut conversation_log,
            &app_config, // Pass by reference
        )
        .await;

        // With the new logic, if max retries are hit and issues persist, we return Ok with partial results.
        assert!(
            result.is_ok(),
            "Expected processing to succeed with partial results after max retries, got: {:?}",
            result.err()
        );
        let output = result.unwrap();

        // Since the LLM never corrects, and the initial response had two summary blocks (which is a count mismatch for Exact(1)),
        // last_attempt_successfully_parsed_blocks for summary should be empty.
        assert_eq!(
            output
                .successfully_parsed_blocks
                .get(&SummaryBlockParser.id())
                .map_or(0, |v| v.len()),
            0
        );

        // The two original summary blocks should be in remaining_raw_blocks because they were not "successfully_parsed"
        // according to the Exact(1) expectation. They were part of `current_closed_blocks` in the last iteration,
        // and since they didn't meet the count criteria for `SummaryBlockParser`, their indices wouldn't be in
        // `last_attempt_claimed_indices` for that parser.
        assert_eq!(output.remaining_raw_blocks.len(), 2);
        assert_eq!(output.remaining_raw_blocks[0].keyword, "summary");
        assert_eq!(output.remaining_raw_blocks[1].keyword, "summary");

        assert_eq!(conversation_log.len(), 1 + MAX_FIX_ATTEMPTS * 2); // Initial Assistant + (3 fixer prompts + 3 assistant responses)
    }

    #[tokio::test]
    async fn test_process_optional_block_present_or_absent() {
        let app_config = get_default_app_config();
        let llm_client_present = MockLLMClient::new(vec![]);
        let llm_client_absent = MockLLMClient::new(vec![]);
        let mut conversation_log = Vec::new();

        let expectations = vec![BlockExpectation {
            parser: Box::new(SummaryBlockParser),
            expected_count: BlockCount::Optional,
        }];

        // Case 1: Optional block is present
        let response_with_summary = "```summary\nOptional summary.\n```";
        let result_present = process_llm_response_with_blocks(
            response_with_summary,
            &expectations,
            "test",
            &[],
            Arc::new(llm_client_present), // Wrap MockLLMClient in Arc for tests
            &[],
            &mut conversation_log,
            &app_config,
        )
        .await;
        assert!(result_present.is_ok());
        assert_eq!(
            result_present
                .unwrap()
                .successfully_parsed_blocks
                .get(&SummaryBlockParser.id())
                .map_or(0, |v| v.len()),
            1
        );
        conversation_log.clear(); // Reset for next test part

        // Case 2: Optional block is absent
        let response_without_summary = "No summary here.";
        let result_absent = process_llm_response_with_blocks(
            response_without_summary,
            &expectations,
            "test",
            &[],
            Arc::new(llm_client_absent), // Wrap MockLLMClient in Arc for tests
            &[],
            &mut conversation_log,
            &app_config,
        )
        .await;
        assert!(result_absent.is_ok());
        assert_eq!(
            result_absent
                .unwrap()
                .successfully_parsed_blocks
                .get(&SummaryBlockParser.id())
                .map_or(0, |v| v.len()),
            0
        );
    }

    #[tokio::test]
    async fn test_process_unclaimed_blocks_returned() {
        let app_config = get_default_app_config();
        let llm_client = MockLLMClient::new(vec![]);
        let mut conversation_log = Vec::new();

        let response_text = "```summary\nMy plan.\n```\n```unknown\nThis is an unknown block.\n```\n```python\nprint('hello')\n```";
        let expectations = vec![
            BlockExpectation {
                parser: Box::new(SummaryBlockParser),
                expected_count: BlockCount::Exact(1),
            }, // No expectation for "unknown" or "python"
        ];

        let result = process_llm_response_with_blocks(
            response_text,
            &expectations,
            "test",
            &[],
            Arc::new(llm_client), // Wrap MockLLMClient in Arc for tests
            &[],
            &mut conversation_log,
            &app_config,
        )
        .await;

        assert!(result.is_ok());
        let output = result.unwrap();
        assert_eq!(
            output
                .successfully_parsed_blocks
                .get(&SummaryBlockParser.id())
                .map_or(0, |v| v.len()),
            1
        );
        assert_eq!(output.remaining_raw_blocks.len(), 2);
        assert_eq!(output.remaining_raw_blocks[0].keyword, "unknown");
        assert_eq!(output.remaining_raw_blocks[1].keyword, "python");
        assert!(output
            .successfully_parsed_blocks
            .get(&SummaryBlockParser.id())
            .is_some());
    }

    #[tokio::test]
    async fn test_process_unclosed_block_fixed_after_retry() {
        let app_config = get_default_app_config();
        let initial_response = "```replace\nThis block is not closed.";
        let corrected_response = "```replace\nThis block is now closed.\n```";
        let llm_client = MockLLMClient::new(vec![corrected_response.to_string()]);

        let expectations = vec![BlockExpectation {
            parser: Box::new(ReplaceBlockParser),
            expected_count: BlockCount::Exact(1),
        }];
        let mut conversation_log = Vec::new();

        let result = process_llm_response_with_blocks(
            &initial_response,
            &expectations,
            "test prompt for unclosed block",
            &[],                  // No files needed for this test
            Arc::new(llm_client), // Wrap MockLLMClient in Arc for tests
            &[],                  // history_prefix
            &mut conversation_log,
            &app_config,
        )
        .await;

        assert!(
            result.is_ok(),
            "Processing failed for unclosed block: {:?}",
            result.err()
        );
        let output = result.unwrap();
        assert_eq!(
            output
                .successfully_parsed_blocks
                .get(&ReplaceBlockParser.id())
                .map_or(0, |v| v.len()),
            1
        );
        assert_eq!(
            output
                .successfully_parsed_blocks
                .get(&ReplaceBlockParser.id())
                .unwrap()[0]
                .content_after_keyword,
            "This block is now closed." // Content between newlines
        );
        assert_eq!(conversation_log.len(), 3); // Initial Assistant Msg + User fixer prompt + Assistant fixed response

        // Check that the fixer prompt mentioned the unclosed block
        // The user's fixer prompt is the second message in this specific sequence for the retry.
        // conversation_log[0] is initial assistant response.
        // conversation_log[1] is user fixer prompt.
        // conversation_log[2] is assistant's corrected response.
        let fixer_prompt_content = &conversation_log[1].content;
        assert!(fixer_prompt_content.contains("critical block formatting error was detected"), "Fixer prompt content did not contain expected 'critical block formatting error was detected' message. Actual: '{}'", fixer_prompt_content);
        assert!(fixer_prompt_content
            .contains("block starting with '```replace' was not properly closed"), "Fixer prompt content did not contain expected message about 'replace' block. Actual: '{}'", fixer_prompt_content);
        assert!(fixer_prompt_content.contains("This block is not closed."));
    }

    #[tokio::test]
    async fn test_process_unclosed_block_persists_after_max_retries() {
        let app_config = get_default_app_config();
        let unclosed_response = "```replace\nStill unclosed";
        let llm_client = MockLLMClient::new(vec![
            unclosed_response.to_string(),
            unclosed_response.to_string(),
            unclosed_response.to_string(),
        ]);

        let expectations = vec![BlockExpectation {
            parser: Box::new(ReplaceBlockParser),
            expected_count: BlockCount::Exact(1),
        }];
        let mut conversation_log = Vec::new();

        let result = process_llm_response_with_blocks(
            unclosed_response,
            &expectations,
            "test prompt",
            &[],
            Arc::new(llm_client), // Wrap MockLLMClient in Arc for tests
            &[],                  // history_prefix
            &mut conversation_log,
            &app_config,
        )
        .await;

        // With the new logic, this should return Ok with partial (likely empty successful) results
        assert!(result.is_ok(), "Expected processing to succeed with partial results after max retries for unclosed block, got: {:?}", result.err());
        let output = result.unwrap();

        // No blocks should be successfully parsed
        assert!(output
            .successfully_parsed_blocks
            .get(&ReplaceBlockParser.id())
            .map_or(true, |v| v.is_empty()));

        // The unclosed block itself is not part of `current_closed_blocks`, so `remaining_raw_blocks` should be empty.
        // The error about the unclosed block is logged.
        assert!(output.remaining_raw_blocks.is_empty());

        // Check conversation log for retries
        assert_eq!(conversation_log.len(), 1 + MAX_FIX_ATTEMPTS * 2); // Initial Assistant + (3 fixer prompts + 3 assistant responses)

        // The warning log (not testable here directly) should indicate the unclosed block and count mismatch.
    }

    #[tokio::test]
    async fn test_process_ignore_unclosed_block_with_unexpected_keyword() {
        let app_config = get_default_app_config();
        let initial_response = "```summary\nThis is a valid summary.\n```\n```rust\nfn main() { // This rust block is unclosed and unexpected";
        let llm_client = MockLLMClient::new(vec![]); // No retries expected

        let expectations = vec![BlockExpectation {
            parser: Box::new(SummaryBlockParser),
            expected_count: BlockCount::Exact(1),
        }];
        let mut conversation_log = Vec::new(); // For retries, should remain empty

        let result = process_llm_response_with_blocks(
            initial_response,
            &expectations,
            "test prompt for ignoring unclosed unexpected block",
            &[],                  // No files needed
            Arc::new(llm_client), // Wrap MockLLMClient in Arc for tests
            &[],                  // history_prefix
            &mut conversation_log,
            &app_config,
        )
        .await;

        assert!(
            result.is_ok(),
            "Processing failed when it should have ignored the unclosed unexpected block: {:?}",
            result.err()
        );
        let output = result.unwrap();

        // Check that the summary block was parsed
        assert_eq!(
            output
                .successfully_parsed_blocks
                .get(&SummaryBlockParser.id())
                .map_or(0, |v| v.len()),
            1
        );
        assert_eq!(
            output
                .successfully_parsed_blocks
                .get(&SummaryBlockParser.id())
                .unwrap()[0]
                .content_after_keyword
                .trim(),
            "This is a valid summary."
        );

        // Check that no retries occurred
        assert_eq!(
            conversation_log.len(),
            1, // Only the initial assistant message should be in the log
            "Expected no retry attempts (so log length 1) as the unclosed block should be ignored."
        );

        // Check that the unclosed 'rust' block is not in remaining_raw_blocks
        assert!(output.remaining_raw_blocks.is_empty(), "Expected remaining_raw_blocks to be empty as the unclosed unexpected block should be ignored, not returned as remaining.");
    }
}
