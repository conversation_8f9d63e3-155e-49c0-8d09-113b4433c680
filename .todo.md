        - in expert editing flow, after retry parsing we end up with all edits duplicated. This may be that either the LLM returned all of them again including the malformed one, or that we add them twice to the list of edits, or something else. In any case we must perform de-duplication/preferably not allow edits for existing ranges to be added if that range already has an edit that was successfully parsed. Figure out why and fix.

      -  │23:14:40|Expert response contains malformed or non-block text. 2 blocks successfully parsed. Attempting to fix remaining text...                                                                                         
      We should not retry if there is non-blcok text, that is expected. We should only retry when there are clearly malformed blocks, such as blocks that are not closed at the end, blocks with invalid internal structure/content that does not match what is required, etc.


            We are working on making rust delimiter fixing really robust. Specifically:
            - if after 3 retries delimiter checking fails for a specific edit, the edit thus manages to pass over delimiter checking currently, and this causes issues as in the next loop the wrong delimiters is the standard to check new edits against which will break consistently. As such we must completely "fail" edits that fail all retries of delimiter checking and not apply the edit at all by just skipping over it (if delimiter checking not turned on/not rust file, obviously this logic gets skipped). 
            - There is also a similar problem in general if starting a task from code with broken delimiters already baked in, running into the same problem that fixing broken delimiter code will keep failing the delimiter checks as the LLM tries to actually fix the count. Thus to solve this we copy a bit from the syntax checking flow of using syn. Where before doing any delimiter checking of any edits for a given file, first syn parse the code in the file if its rust and delimiter checking is on, and if it errors with specifically "cannot parse string into token stream" that means delimiters are broken already in that file (we have verified this is the case 100%), and in that case dont do delimiter checking/fixing inferencing at all for that file during this task and just apply the edit. This should be done on every retry of course. This will thus only trigger delimiter checking when the code that is  having edits applied on is already in a good state.
            Think really hard about both of these points and write a detailed plan for each and implement

- update retries from 3x -> 5x

- implement a `restore_previous_session_models` in file config where if its set to false, then dont load any of the models (default, expert, retry, etc.) from the last session, only load the rest of the previous session. It should default to false in the config.

- replace `/ask-no-history*` commands with `/ask-no-context`  (including task info variant) which just does a temporal inference to the LLM with just the question the user asked, absolutely nothing else added to the prompt.

- Expert and no files only for auto-research currently is broken, as when 0 files it skips over auto-research. Fix this.
│22:21:26|Auto-research mode is 'ExpertAndNoFilesOnly'. Standard auto-research decision process is skipped. Resear││                           │
│         ch may be initiated by the expert model.                                                                ││                           │
│22:21:26|Initial auto-research phase outcome: Auto-research is configured for 'ExpertAndNoFilesOnly' mode. Resear││                           │
│         ch may be initiated by the expert model if configured.                                                  ││                           │

- We now want to implement A sound system into the application. In the config we want a `sounds_master_volume` that defaults to 25 (out of max 100, and min 0, where any values outside of the range are capped to min or max closest) and a `submit_sound` which can be either true/false/`<path>` (defaults false) where if its true it uses a unix-default included confirmation sound akin to bell but nicer if there is one, false it doesnt play at all, and if a path is supplied it attempts to play The file supplied at the path as a music file (mp3/wav/ogg/etc.). Then specifically for the submit sound we want to hook this up into interactive so that whenever is submitted weather by the user pressing enter or when an input is processed from the HTTP server then we play the submit sound using the master volume. Figure out the best cratesThat would be good to use for thisThat is not too largeAnd provides us both with playing many different audio typesAnd specifying a volume.

- add `old_code` field to applied edits, and then for `/continue` command, include both the old/new code for the previous task. (also need to think about storing all of the edits across retries of the last task in a clean way ideally)

- For summary block processing for both the editing plan summary and the edits summary, if there are 0 properly formatted blocks found, then just remove any lines that start with triple back ticks "```
" and use the LLM's response as is after that.
- If a command is submitted via http api, then we should first clear the input box before processing it.

- implement `analysis_model` and `auto_analysis` (true/false) and `max_analysis_retries`, where after auto-test succeeds if its set to true then we inference the LLM (if using default model then full prompt cache history, else just limited to required information in small prompt) with a new analysis prompt where we first try to call git via bash in working directory and fetch the diffs of all files (if git not available at runtime then just take the edits from this latest task to keep simple), and then construct a prompt where we fetch each file that was edited (with edits applied) and put them at the start of the prompt in a first message (skip this if using default model) and then we add all of the diffs that we fetched earlier, and then we ask the LLM if after analyzing all of the diffs have these edits successfully achieved the user's task (insert user message for task). If so then simply respond with 1 sentence confirming it is sufficient, otherwise provide an `issues` block that in 1-5 paragraphs explains in detail what is missing or wrongly implemented compared to what the user has asked so that another AI can take over with planning and implementation (the focus is fully on analyzing and uncovering issues, and giving solutions). Unless the user specified otherwise, small quality of life/cleanliness/formatting/standardizing problems, or other such things which are nice to have but don't impede on effectiveness of the implementation do not count as issues. Only focus on whether the code implements the task the user specified and will run as required (the code has already passed syntax checking/compliation/etc.)

- later add `/analyze-latest-task` as a form of auto-continue that takes the previous task initial message and re-runs the analysis prompt with the analysis model.

- probably fixed, when parsing `range-replace` blocks returned from expert-editing and rust syntax fixing, if we run into any parsing errors on the blocks, then ideally for the fixing retry inferences we should first fully remove all properly parsed blocks from the original llm response and only leave all of the other text/malformed blocks in the string which will be used for the re-inference to ask for fix. Figure out the best way to implement this in a clean way that fits in with existing flows as best as possible

- Probably fixed, Delimiter fix inferencing full prompt when using non-default model for some reason includes more content than just the specific codeblock to fix. LLM has responded with fixes for *ALL* range-replace blocks provided, which means that for some reason previous messages were included and sent to the LLM even though it is non default and it should only get the delimiter-related prompt to fix (very small/precise to keep it clean and simple). This might be a problem that delimiter fixing first inference vs. its retries are different potentially (this was a problem previously, not sure if fixed) which they should be unified if they are separate and we should just go straight into retry #1 without anything before, and make sure its just the required prompt and nothing else (if using non-default model, meaning we dont need to maintain prompt cache. if default model then maintain the history for prompt caching of course). Figure out this issue after thinking hard, and fix.

- Probably fixed, needs to be checked in practice: When research auto-decision is inferenced, whether true or forced whether default/decision/expert model, neither the prompt nor the response from the LLM should be included in the message history. Right now they are and are making the history more confusing than they need to be for the LLM.

- First forced then true/false option seem to be wrong. They apply to both the first try of the task + the first retry, while we only want it to work on the first try when retries = 0.

- Check and make sure creation of new files automatically creates new folders as well if folder doesnt exist.

- Optimization:  for standard editing flow, potentially have a temporal inference step that only triggers if the user's input text is over 750 characters, where it summarizes the user's goal down to 1-3 lines, and then that is used when asking LLM for each `replace` block edit in the prompt.

- for llm inferencing request code, if a request fails, automatically retry up to x times with 1 second of wait in between which doubles each failure. the number of times this is allowed to happen is controlled by a new config value called `http_request_max_retries` which has a default of 3, and users can set to whatever. Whenever a request fails and we intend to retry a request, we debug log `HTTP request failed. Waiting and resending prompt to LLM again in N seconds.`, and if we hit max retries and we are done `HTTP request failed. Hit max number of retries. Continuing without receiving response from LLM.`

- for task summary, if there are no previous tasks then have the prompt be like it is right now. If retries > 0 and task summary starts, then update the prompt so it explicitly only summarizes the errors in the latest auto-test and the edits that have been made since.
- also for summary block if the summary block has no closing but does start, just take the whole message and add a closing block for summary automatically to avoid wasteful reprompting the llm again to fix (should still happen if we dont detect summary block opening at all)

- If retries > 0 (meaning we are in a retry) and asking the LLM whether it wants to research or not, in the prompt if retries > 0 tell the LLM that if there are auto-test errors which failed because incorrect varibles/structs/types/functions/methods/classes/traits/etc. were used, then it is highly highly recommended that the LLM returns a `research` block to have the assistant find the relevant files which hold the definitions.

- try replacing 0-0 in range blocks with `new` keyword instead for creating new files to see if less problems for llms

- Think about potentially a "patch file" approach to expert editing, where you take the response of the expert and if retries are required, then you go through a standard editing flow to replace lines in the failed to be parsed patch file to get it to a point where its valid and can be applied. Might be worth later on to see if its more effective.

- See if cancelling task/closing lledit, if possible to more cleanly kill http request so llama.cpp server stops

- finish inference crate integration and get it fully working

- Use `RUSTC_BOOTSTRAP=1 RUSTDOCFLAGS="-Z unstable-options --output-format json" cargo doc --no-deps` +  <https://github.com/tqwewe/rustdoc-md>,
and then implement a custom `markdown_doc_generator.rs` which implements logic that manually goes through all the generated .json doc files in `target/doc` and then creates a `target/doc_md` folder, where for each json file it creates a folder
named the same as the json file, and then creates a series of smaller markdown files based on the top level heading from the output generated markdown via the crate. The end goal is to have a nice structure similar to the original html from `cargo doc` but all content in markdown form.
- Create a new folder called `programming-language-integrations` with a `rust` folder inside, and create an initial `detection.rs` and `prompts.rs`
- In these add custom function to check if we detect its a rust project and working dir is at the project root (Cargo.toml in working directory)
- The implement function that will add to auto/research prompt that if it detects a `Cargo.toml` in the working directory then it returns a Some() and the contents of cargo.toml get included in the auto/research planing prompt.
- Also implement a detection func and prompt func each, which if it detects that `/target/doc` exists and is not empty, then tell the LLM in the planning prompt that `This rust project generated docs in the 'target/doc' folder for all of the crates it is using. If you require finding up-to-date info on latest function signatures, or how certain structs/traits/functions work and integrate together, researching into the doc folder is recommended.`

- think about the full implications of having pruned/summarized tasks be part of task info. Does that make sense to the LLM if say task 1/2 are pruned and put in task info, but task 3/4 are not pruned yet? Will the tasks 3/4 message history be earlier chronologically for the LLM than 1/2 in that case? Also it may not make the most sense to have it in task info as some users may want to clear task info and add their own info to it at any point. Potentially come up with better solution of holding the original Task structs in history, just cut the message history in it to just be the summaries maybe?

- Implement some UI for denoting how many previous tasks are part of the conversation history & then add a command to clear conversation history. (think how to chain this all with summarizer/relevance)
- implement `/task-history-clear` `/qa-history-clear` and `/all-history-clear` which clears both. Make sure task info is maintained even when any of these are cleared.

- if a request to LLM fails then we should retry 5 times, doubling the time in between each attempt twice, first instant retry, then 2s, 4s, etc.
- if a request takes longer than 30 minutes it should time out and retry

- Update all retries across the project from 3 to 15.
- After auto-research is finished, ask the LLM again if it has all the files it needs, or if it wants to ask a research assistant to find more files (again).

- At the end of editing flow, do a temporal inference that lists all of the file paths of files we have in context and asks the LLM which files are not relevant to the task at hand and don't provide any useful context.
  Then we parse that list, and somewhere in application state we keep a list called `to_be_pruned_files` and replace that list every time with the list of files that the LLM provided.
  Then whenever we prune the old tasks and replace them with summaries, we also implement this file pruning logic which removes from context any files which are still part of context which are on the to be pruned list (and then clear the to be pruned list).

- Also design/implement a "Relevance weighting" system where at the end of edits you ask the LLM to list any files it deemed irrelevant and maintain that weighting in the back.
Then when context size is hit, first fill in old prompts with summaries, and if still doesnt fit then do JIT file selection trimming based on irrelevance weighting from previous messages.
(think this through more for best solution later on)

- later on go back and fix the `range` block parsing as it seems to be overly strict and failing on valid blocks:

``
**Action Plan:**

- Adjust the logic in `refresh_config` to correctly merge configurations without relying on default values for CLI arguments.

### Detailed Ranges

#### 1. Verify and Correct Declaration of `refresh_config`

Ensure `refresh_config` is defined and exported properly.
```range
src/config/app_config.rs
678-690
```
2. Implement Default Trait for Args

Implement the Default trait for Args.
```range
src/cli.rs
18-30
```
Range

3. Adjust Logic in refresh_config

Ensure that refresh_config correctly merges configurations.
```range
src/config/app_config.rs
679-682
```
Thus these are all of the edits ...
``        - in expert editing flow, after retry parsing we end up with all edits duplicated. This may be that either the LLM returned all of them again including the malformed one, or that we add them twice to the list of edits, or something else. In any case we must perform de-duplication/preferably not allow edits for existing ranges to be added if that range already has an edit that was successfully parsed. Figure out why and fix.

      -  │23:14:40|Expert response contains malformed or non-block text. 2 blocks successfully parsed. Attempting to fix remaining text...                                                                                         
      We should not retry if there is non-blcok text, that is expected. We should only retry when there are clearly malformed blocks, such as blocks that are not closed at the end, blocks with invalid internal structure/content that does not match what is required, etc.


            We are working on making rust delimiter fixing really robust. Specifically:
            - if after 3 retries delimiter checking fails for a specific edit, the edit thus manages to pass over delimiter checking currently, and this causes issues as in the next loop the wrong delimiters is the standard to check new edits against which will break consistently. As such we must completely "fail" edits that fail all retries of delimiter checking and not apply the edit at all by just skipping over it (if delimiter checking not turned on/not rust file, obviously this logic gets skipped). 
            - There is also a similar problem in general if starting a task from code with broken delimiters already baked in, running into the same problem that fixing broken delimiter code will keep failing the delimiter checks as the LLM tries to actually fix the count. Thus to solve this we copy a bit from the syntax checking flow of using syn. Where before doing any delimiter checking of any edits for a given file, first syn parse the code in the file if its rust and delimiter checking is on, and if it errors with specifically "cannot parse string into token stream" that means delimiters are broken already in that file (we have verified this is the case 100%), and in that case dont do delimiter checking/fixing inferencing at all for that file during this task and just apply the edit. This should be done on every retry of course. This will thus only trigger delimiter checking when the code that is  having edits applied on is already in a good state.
            Think really hard about both of these points and write a detailed plan for each and implement

- update retries from 3x -> 5x

- implement a `restore_previous_session_models` in file config where if its set to false, then dont load any of the models (default, expert, retry, etc.) from the last session, only load the rest of the previous session. It should default to false in the config.

- replace `/ask-no-history*` commands with `/ask-no-context`  (including task info variant) which just does a temporal inference to the LLM with just the question the user asked, absolutely nothing else added to the prompt.

- Expert and no files only for auto-research currently is broken, as when 0 files it skips over auto-research. Fix this.
│22:21:26|Auto-research mode is 'ExpertAndNoFilesOnly'. Standard auto-research decision process is skipped. Resear││                           │
│         ch may be initiated by the expert model.                                                                ││                           │
│22:21:26|Initial auto-research phase outcome: Auto-research is configured for 'ExpertAndNoFilesOnly' mode. Resear││                           │
│         ch may be initiated by the expert model if configured.                                                  ││                           │

- We now want to implement A sound system into the application. In the config we want a `sounds_master_volume` that defaults to 25 (out of max 100, and min 0, where any values outside of the range are capped to min or max closest) and a `submit_sound` which can be either true/false/`<path>` (defaults false) where if its true it uses a unix-default included confirmation sound akin to bell but nicer if there is one, false it doesnt play at all, and if a path is supplied it attempts to play The file supplied at the path as a music file (mp3/wav/ogg/etc.). Then specifically for the submit sound we want to hook this up into interactive so that whenever is submitted weather by the user pressing enter or when an input is processed from the HTTP server then we play the submit sound using the master volume. Figure out the best cratesThat would be good to use for thisThat is not too largeAnd provides us both with playing many different audio typesAnd specifying a volume.

- add `old_code` field to applied edits, and then for `/continue` command, include both the old/new code for the previous task. (also need to think about storing all of the edits across retries of the last task in a clean way ideally)

- For summary block processing for both the editing plan summary and the edits summary, if there are 0 properly formatted blocks found, then just remove any lines that start with triple back ticks "
```
" and use the LLM's response as is after that.
- If a command is submitted via http api, then we should first clear the input box before processing it.

- implement `analysis_model` and `auto_analysis` (true/false) and `max_analysis_retries`, where after auto-test succeeds if its set to true then we inference the LLM (if using default model then full prompt cache history, else just limited to required information in small prompt) with a new analysis prompt where we first try to call git via bash in working directory and fetch the diffs of all files (if git not available at runtime then just take the edits from this latest task to keep simple), and then construct a prompt where we fetch each file that was edited (with edits applied) and put them at the start of the prompt in a first message (skip this if using default model) and then we add all of the diffs that we fetched earlier, and then we ask the LLM if after analyzing all of the diffs have these edits successfully achieved the user's task (insert user message for task). If so then simply respond with 1 sentence confirming it is sufficient, otherwise provide an `issues` block that in 1-5 paragraphs explains in detail what is missing or wrongly implemented compared to what the user has asked so that another AI can take over with planning and implementation (the focus is fully on analyzing and uncovering issues, and giving solutions). Unless the user specified otherwise, small quality of life/cleanliness/formatting/standardizing problems, or other such things which are nice to have but don't impede on effectiveness of the implementation do not count as issues. Only focus on whether the code implements the task the user specified and will run as required (the code has already passed syntax checking/compliation/etc.)

- later add `/analyze-latest-task` as a form of auto-continue that takes the previous task initial message and re-runs the analysis prompt with the analysis model.

- probably fixed, when parsing `range-replace` blocks returned from expert-editing and rust syntax fixing, if we run into any parsing errors on the blocks, then ideally for the fixing retry inferences we should first fully remove all properly parsed blocks from the original llm response and only leave all of the other text/malformed blocks in the string which will be used for the re-inference to ask for fix. Figure out the best way to implement this in a clean way that fits in with existing flows as best as possible

- Probably fixed, Delimiter fix inferencing full prompt when using non-default model for some reason includes more content than just the specific codeblock to fix. LLM has responded with fixes for *ALL* range-replace blocks provided, which means that for some reason previous messages were included and sent to the LLM even though it is non default and it should only get the delimiter-related prompt to fix (very small/precise to keep it clean and simple). This might be a problem that delimiter fixing first inference vs. its retries are different potentially (this was a problem previously, not sure if fixed) which they should be unified if they are separate and we should just go straight into retry #1 without anything before, and make sure its just the required prompt and nothing else (if using non-default model, meaning we dont need to maintain prompt cache. if default model then maintain the history for prompt caching of course). Figure out this issue after thinking hard, and fix.

- Probably fixed, needs to be checked in practice: When research auto-decision is inferenced, whether true or forced whether default/decision/expert model, neither the prompt nor the response from the LLM should be included in the message history. Right now they are and are making the history more confusing than they need to be for the LLM.

- First forced then true/false option seem to be wrong. They apply to both the first try of the task + the first retry, while we only want it to work on the first try when retries = 0.

- Check and make sure creation of new files automatically creates new folders as well if folder doesnt exist.

- Optimization:  for standard editing flow, potentially have a temporal inference step that only triggers if the user's input text is over 750 characters, where it summarizes the user's goal down to 1-3 lines, and then that is used when asking LLM for each `replace` block edit in the prompt.

- for llm inferencing request code, if a request fails, automatically retry up to x times with 1 second of wait in between which doubles each failure. the number of times this is allowed to happen is controlled by a new config value called `http_request_max_retries` which has a default of 3, and users can set to whatever. Whenever a request fails and we intend to retry a request, we debug log `HTTP request failed. Waiting and resending prompt to LLM again in N seconds.`, and if we hit max retries and we are done `HTTP request failed. Hit max number of retries. Continuing without receiving response from LLM.`

- for task summary, if there are no previous tasks then have the prompt be like it is right now. If retries > 0 and task summary starts, then update the prompt so it explicitly only summarizes the errors in the latest auto-test and the edits that have been made since.
- also for summary block if the summary block has no closing but does start, just take the whole message and add a closing block for summary automatically to avoid wasteful reprompting the llm again to fix (should still happen if we dont detect summary block opening at all)

- If retries > 0 (meaning we are in a retry) and asking the LLM whether it wants to research or not, in the prompt if retries > 0 tell the LLM that if there are auto-test errors which failed because incorrect varibles/structs/types/functions/methods/classes/traits/etc. were used, then it is highly highly recommended that the LLM returns a `research` block to have the assistant find the relevant files which hold the definitions.

- try replacing 0-0 in range blocks with `new` keyword instead for creating new files to see if less problems for llms

- Think about potentially a "patch file" approach to expert editing, where you take the response of the expert and if retries are required, then you go through a standard editing flow to replace lines in the failed to be parsed patch file to get it to a point where its valid and can be applied. Might be worth later on to see if its more effective.

- See if cancelling task/closing lledit, if possible to more cleanly kill http request so llama.cpp server stops

- finish inference crate integration and get it fully working

- Use `RUSTC_BOOTSTRAP=1 RUSTDOCFLAGS="-Z unstable-options --output-format json" cargo doc --no-deps` +  <https://github.com/tqwewe/rustdoc-md>,
and then implement a custom `markdown_doc_generator.rs` which implements logic that manually goes through all the generated .json doc files in `target/doc` and then creates a `target/doc_md` folder, where for each json file it creates a folder
named the same as the json file, and then creates a series of smaller markdown files based on the top level heading from the output generated markdown via the crate. The end goal is to have a nice structure similar to the original html from `cargo doc` but all content in markdown form.
- Create a new folder called `programming-language-integrations` with a `rust` folder inside, and create an initial `detection.rs` and `prompts.rs`
- In these add custom function to check if we detect its a rust project and working dir is at the project root (Cargo.toml in working directory)
- The implement function that will add to auto/research prompt that if it detects a `Cargo.toml` in the working directory then it returns a Some() and the contents of cargo.toml get included in the auto/research planing prompt.
- Also implement a detection func and prompt func each, which if it detects that `/target/doc` exists and is not empty, then tell the LLM in the planning prompt that `This rust project generated docs in the 'target/doc' folder for all of the crates it is using. If you require finding up-to-date info on latest function signatures, or how certain structs/traits/functions work and integrate together, researching into the doc folder is recommended.`

- think about the full implications of having pruned/summarized tasks be part of task info. Does that make sense to the LLM if say task 1/2 are pruned and put in task info, but task 3/4 are not pruned yet? Will the tasks 3/4 message history be earlier chronologically for the LLM than 1/2 in that case? Also it may not make the most sense to have it in task info as some users may want to clear task info and add their own info to it at any point. Potentially come up with better solution of holding the original Task structs in history, just cut the message history in it to just be the summaries maybe?

- Implement some UI for denoting how many previous tasks are part of the conversation history & then add a command to clear conversation history. (think how to chain this all with summarizer/relevance)
- implement `/task-history-clear` `/qa-history-clear` and `/all-history-clear` which clears both. Make sure task info is maintained even when any of these are cleared.

- if a request to LLM fails then we should retry 5 times, doubling the time in between each attempt twice, first instant retry, then 2s, 4s, etc.
- if a request takes longer than 30 minutes it should time out and retry

- Update all retries across the project from 3 to 15.
- After auto-research is finished, ask the LLM again if it has all the files it needs, or if it wants to ask a research assistant to find more files (again).

- At the end of editing flow, do a temporal inference that lists all of the file paths of files we have in context and asks the LLM which files are not relevant to the task at hand and don't provide any useful context.
  Then we parse that list, and somewhere in application state we keep a list called `to_be_pruned_files` and replace that list every time with the list of files that the LLM provided.
  Then whenever we prune the old tasks and replace them with summaries, we also implement this file pruning logic which removes from context any files which are still part of context which are on the to be pruned list (and then clear the to be pruned list).

- Also design/implement a "Relevance weighting" system where at the end of edits you ask the LLM to list any files it deemed irrelevant and maintain that weighting in the back.
Then when context size is hit, first fill in old prompts with summaries, and if still doesnt fit then do JIT file selection trimming based on irrelevance weighting from previous messages.
(think this through more for best solution later on)

- later on go back and fix the `range` block parsing as it seems to be overly strict and failing on valid blocks:

``
**Action Plan:**

- Adjust the logic in `refresh_config` to correctly merge configurations without relying on default values for CLI arguments.

### Detailed Ranges

#### 1. Verify and Correct Declaration of `refresh_config`

Ensure `refresh_config` is defined and exported properly.
```range
src/config/app_config.rs
678-690
```
2. Implement Default Trait for Args

Implement the Default trait for Args.
```range
src/cli.rs
18-30
```
Range

3. Adjust Logic in refresh_config

Ensure that refresh_config correctly merges configurations.
```range
src/config/app_config.rs
679-682
```
Thus these are all of the edits ...
``        - in expert editing flow, after retry parsing we end up with all edits duplicated. This may be that either the LLM returned all of them again including the malformed one, or that we add them twice to the list of edits, or something else. In any case we must perform de-duplication/preferably not allow edits for existing ranges to be added if that range already has an edit that was successfully parsed. Figure out why and fix.

      -  │23:14:40|Expert response contains malformed or non-block text. 2 blocks successfully parsed. Attempting to fix remaining text...                                                                                         
      We should not retry if there is non-blcok text, that is expected. We should only retry when there are clearly malformed blocks, such as blocks that are not closed at the end, blocks with invalid internal structure/content that does not match what is required, etc.


            We are working on making rust delimiter fixing really robust. Specifically:
            - if after 3 retries delimiter checking fails for a specific edit, the edit thus manages to pass over delimiter checking currently, and this causes issues as in the next loop the wrong delimiters is the standard to check new edits against which will break consistently. As such we must completely "fail" edits that fail all retries of delimiter checking and not apply the edit at all by just skipping over it (if delimiter checking not turned on/not rust file, obviously this logic gets skipped). 
            - There is also a similar problem in general if starting a task from code with broken delimiters already baked in, running into the same problem that fixing broken delimiter code will keep failing the delimiter checks as the LLM tries to actually fix the count. Thus to solve this we copy a bit from the syntax checking flow of using syn. Where before doing any delimiter checking of any edits for a given file, first syn parse the code in the file if its rust and delimiter checking is on, and if it errors with specifically "cannot parse string into token stream" that means delimiters are broken already in that file (we have verified this is the case 100%), and in that case dont do delimiter checking/fixing inferencing at all for that file during this task and just apply the edit. This should be done on every retry of course. This will thus only trigger delimiter checking when the code that is  having edits applied on is already in a good state.
            Think really hard about both of these points and write a detailed plan for each and implement

- update retries from 3x -> 5x

- implement a `restore_previous_session_models` in file config where if its set to false, then dont load any of the models (default, expert, retry, etc.) from the last session, only load the rest of the previous session. It should default to false in the config.

- replace `/ask-no-history*` commands with `/ask-no-context`  (including task info variant) which just does a temporal inference to the LLM with just the question the user asked, absolutely nothing else added to the prompt.

- Expert and no files only for auto-research currently is broken, as when 0 files it skips over auto-research. Fix this.
│22:21:26|Auto-research mode is 'ExpertAndNoFilesOnly'. Standard auto-research decision process is skipped. Resear││                           │
│         ch may be initiated by the expert model.                                                                ││                           │
│22:21:26|Initial auto-research phase outcome: Auto-research is configured for 'ExpertAndNoFilesOnly' mode. Resear││                           │
│         ch may be initiated by the expert model if configured.                                                  ││                           │

- We now want to implement A sound system into the application. In the config we want a `sounds_master_volume` that defaults to 25 (out of max 100, and min 0, where any values outside of the range are capped to min or max closest) and a `submit_sound` which can be either true/false/`<path>` (defaults false) where if its true it uses a unix-default included confirmation sound akin to bell but nicer if there is one, false it doesnt play at all, and if a path is supplied it attempts to play The file supplied at the path as a music file (mp3/wav/ogg/etc.). Then specifically for the submit sound we want to hook this up into interactive so that whenever is submitted weather by the user pressing enter or when an input is processed from the HTTP server then we play the submit sound using the master volume. Figure out the best cratesThat would be good to use for thisThat is not too largeAnd provides us both with playing many different audio typesAnd specifying a volume.

- add `old_code` field to applied edits, and then for `/continue` command, include both the old/new code for the previous task. (also need to think about storing all of the edits across retries of the last task in a clean way ideally)

- For summary block processing for both the editing plan summary and the edits summary, if there are 0 properly formatted blocks found, then just remove any lines that start with triple back ticks "
```
" and use the LLM's response as is after that.
- If a command is submitted via http api, then we should first clear the input box before processing it.

- implement `analysis_model` and `auto_analysis` (true/false) and `max_analysis_retries`, where after auto-test succeeds if its set to true then we inference the LLM (if using default model then full prompt cache history, else just limited to required information in small prompt) with a new analysis prompt where we first try to call git via bash in working directory and fetch the diffs of all files (if git not available at runtime then just take the edits from this latest task to keep simple), and then construct a prompt where we fetch each file that was edited (with edits applied) and put them at the start of the prompt in a first message (skip this if using default model) and then we add all of the diffs that we fetched earlier, and then we ask the LLM if after analyzing all of the diffs have these edits successfully achieved the user's task (insert user message for task). If so then simply respond with 1 sentence confirming it is sufficient, otherwise provide an `issues` block that in 1-5 paragraphs explains in detail what is missing or wrongly implemented compared to what the user has asked so that another AI can take over with planning and implementation (the focus is fully on analyzing and uncovering issues, and giving solutions). Unless the user specified otherwise, small quality of life/cleanliness/formatting/standardizing problems, or other such things which are nice to have but don't impede on effectiveness of the implementation do not count as issues. Only focus on whether the code implements the task the user specified and will run as required (the code has already passed syntax checking/compliation/etc.)

- later add `/analyze-latest-task` as a form of auto-continue that takes the previous task initial message and re-runs the analysis prompt with the analysis model.

- probably fixed, when parsing `range-replace` blocks returned from expert-editing and rust syntax fixing, if we run into any parsing errors on the blocks, then ideally for the fixing retry inferences we should first fully remove all properly parsed blocks from the original llm response and only leave all of the other text/malformed blocks in the string which will be used for the re-inference to ask for fix. Figure out the best way to implement this in a clean way that fits in with existing flows as best as possible

- Probably fixed, Delimiter fix inferencing full prompt when using non-default model for some reason includes more content than just the specific codeblock to fix. LLM has responded with fixes for *ALL* range-replace blocks provided, which means that for some reason previous messages were included and sent to the LLM even though it is non default and it should only get the delimiter-related prompt to fix (very small/precise to keep it clean and simple). This might be a problem that delimiter fixing first inference vs. its retries are different potentially (this was a problem previously, not sure if fixed) which they should be unified if they are separate and we should just go straight into retry #1 without anything before, and make sure its just the required prompt and nothing else (if using non-default model, meaning we dont need to maintain prompt cache. if default model then maintain the history for prompt caching of course). Figure out this issue after thinking hard, and fix.

- Probably fixed, needs to be checked in practice: When research auto-decision is inferenced, whether true or forced whether default/decision/expert model, neither the prompt nor the response from the LLM should be included in the message history. Right now they are and are making the history more confusing than they need to be for the LLM.

- First forced then true/false option seem to be wrong. They apply to both the first try of the task + the first retry, while we only want it to work on the first try when retries = 0.

- Check and make sure creation of new files automatically creates new folders as well if folder doesnt exist.

- Optimization:  for standard editing flow, potentially have a temporal inference step that only triggers if the user's input text is over 750 characters, where it summarizes the user's goal down to 1-3 lines, and then that is used when asking LLM for each `replace` block edit in the prompt.

- for llm inferencing request code, if a request fails, automatically retry up to x times with 1 second of wait in between which doubles each failure. the number of times this is allowed to happen is controlled by a new config value called `http_request_max_retries` which has a default of 3, and users can set to whatever. Whenever a request fails and we intend to retry a request, we debug log `HTTP request failed. Waiting and resending prompt to LLM again in N seconds.`, and if we hit max retries and we are done `HTTP request failed. Hit max number of retries. Continuing without receiving response from LLM.`

- for task summary, if there are no previous tasks then have the prompt be like it is right now. If retries > 0 and task summary starts, then update the prompt so it explicitly only summarizes the errors in the latest auto-test and the edits that have been made since.
- also for summary block if the summary block has no closing but does start, just take the whole message and add a closing block for summary automatically to avoid wasteful reprompting the llm again to fix (should still happen if we dont detect summary block opening at all)

- If retries > 0 (meaning we are in a retry) and asking the LLM whether it wants to research or not, in the prompt if retries > 0 tell the LLM that if there are auto-test errors which failed because incorrect varibles/structs/types/functions/methods/classes/traits/etc. were used, then it is highly highly recommended that the LLM returns a `research` block to have the assistant find the relevant files which hold the definitions.

- try replacing 0-0 in range blocks with `new` keyword instead for creating new files to see if less problems for llms

- Think about potentially a "patch file" approach to expert editing, where you take the response of the expert and if retries are required, then you go through a standard editing flow to replace lines in the failed to be parsed patch file to get it to a point where its valid and can be applied. Might be worth later on to see if its more effective.

- See if cancelling task/closing lledit, if possible to more cleanly kill http request so llama.cpp server stops

- finish inference crate integration and get it fully working

- Use `RUSTC_BOOTSTRAP=1 RUSTDOCFLAGS="-Z unstable-options --output-format json" cargo doc --no-deps` +  <https://github.com/tqwewe/rustdoc-md>,
and then implement a custom `markdown_doc_generator.rs` which implements logic that manually goes through all the generated .json doc files in `target/doc` and then creates a `target/doc_md` folder, where for each json file it creates a folder
named the same as the json file, and then creates a series of smaller markdown files based on the top level heading from the output generated markdown via the crate. The end goal is to have a nice structure similar to the original html from `cargo doc` but all content in markdown form.
- Create a new folder called `programming-language-integrations` with a `rust` folder inside, and create an initial `detection.rs` and `prompts.rs`
- In these add custom function to check if we detect its a rust project and working dir is at the project root (Cargo.toml in working directory)
- The implement function that will add to auto/research prompt that if it detects a `Cargo.toml` in the working directory then it returns a Some() and the contents of cargo.toml get included in the auto/research planing prompt.
- Also implement a detection func and prompt func each, which if it detects that `/target/doc` exists and is not empty, then tell the LLM in the planning prompt that `This rust project generated docs in the 'target/doc' folder for all of the crates it is using. If you require finding up-to-date info on latest function signatures, or how certain structs/traits/functions work and integrate together, researching into the doc folder is recommended.`

- think about the full implications of having pruned/summarized tasks be part of task info. Does that make sense to the LLM if say task 1/2 are pruned and put in task info, but task 3/4 are not pruned yet? Will the tasks 3/4 message history be earlier chronologically for the LLM than 1/2 in that case? Also it may not make the most sense to have it in task info as some users may want to clear task info and add their own info to it at any point. Potentially come up with better solution of holding the original Task structs in history, just cut the message history in it to just be the summaries maybe?

- Implement some UI for denoting how many previous tasks are part of the conversation history & then add a command to clear conversation history. (think how to chain this all with summarizer/relevance)
- implement `/task-history-clear` `/qa-history-clear` and `/all-history-clear` which clears both. Make sure task info is maintained even when any of these are cleared.

- if a request to LLM fails then we should retry 5 times, doubling the time in between each attempt twice, first instant retry, then 2s, 4s, etc.
- if a request takes longer than 30 minutes it should time out and retry

- Update all retries across the project from 3 to 15.
- After auto-research is finished, ask the LLM again if it has all the files it needs, or if it wants to ask a research assistant to find more files (again).

- At the end of editing flow, do a temporal inference that lists all of the file paths of files we have in context and asks the LLM which files are not relevant to the task at hand and don't provide any useful context.
  Then we parse that list, and somewhere in application state we keep a list called `to_be_pruned_files` and replace that list every time with the list of files that the LLM provided.
  Then whenever we prune the old tasks and replace them with summaries, we also implement this file pruning logic which removes from context any files which are still part of context which are on the to be pruned list (and then clear the to be pruned list).

- Also design/implement a "Relevance weighting" system where at the end of edits you ask the LLM to list any files it deemed irrelevant and maintain that weighting in the back.
Then when context size is hit, first fill in old prompts with summaries, and if still doesnt fit then do JIT file selection trimming based on irrelevance weighting from previous messages.
(think this through more for best solution later on)

- later on go back and fix the `range` block parsing as it seems to be overly strict and failing on valid blocks:

``
**Action Plan:**

- Adjust the logic in `refresh_config` to correctly merge configurations without relying on default values for CLI arguments.

### Detailed Ranges

#### 1. Verify and Correct Declaration of `refresh_config`

Ensure `refresh_config` is defined and exported properly.
```range
src/config/app_config.rs
678-690
```
2. Implement Default Trait for Args

Implement the Default trait for Args.
```range
src/cli.rs
18-30
```
Range

3. Adjust Logic in refresh_config

Ensure that refresh_config correctly merges configurations.
```range
src/config/app_config.rs
679-682
```
Thus these are all of the edits ...
``        - in expert editing flow, after retry parsing we end up with all edits duplicated. This may be that either the LLM returned all of them again including the malformed one, or that we add them twice to the list of edits, or something else. In any case we must perform de-duplication/preferably not allow edits for existing ranges to be added if that range already has an edit that was successfully parsed. Figure out why and fix.

      -  │23:14:40|Expert response contains malformed or non-block text. 2 blocks successfully parsed. Attempting to fix remaining text...                                                                                         
      We should not retry if there is non-blcok text, that is expected. We should only retry when there are clearly malformed blocks, such as blocks that are not closed at the end, blocks with invalid internal structure/content that does not match what is required, etc.


            We are working on making rust delimiter fixing really robust. Specifically:
            - if after 3 retries delimiter checking fails for a specific edit, the edit thus manages to pass over delimiter checking currently, and this causes issues as in the next loop the wrong delimiters is the standard to check new edits against which will break consistently. As such we must completely "fail" edits that fail all retries of delimiter checking and not apply the edit at all by just skipping over it (if delimiter checking not turned on/not rust file, obviously this logic gets skipped). 
            - There is also a similar problem in general if starting a task from code with broken delimiters already baked in, running into the same problem that fixing broken delimiter code will keep failing the delimiter checks as the LLM tries to actually fix the count. Thus to solve this we copy a bit from the syntax checking flow of using syn. Where before doing any delimiter checking of any edits for a given file, first syn parse the code in the file if its rust and delimiter checking is on, and if it errors with specifically "cannot parse string into token stream" that means delimiters are broken already in that file (we have verified this is the case 100%), and in that case dont do delimiter checking/fixing inferencing at all for that file during this task and just apply the edit. This should be done on every retry of course. This will thus only trigger delimiter checking when the code that is  having edits applied on is already in a good state.
            Think really hard about both of these points and write a detailed plan for each and implement

- update retries from 3x -> 5x

- implement a `restore_previous_session_models` in file config where if its set to false, then dont load any of the models (default, expert, retry, etc.) from the last session, only load the rest of the previous session. It should default to false in the config.

- replace `/ask-no-history*` commands with `/ask-no-context`  (including task info variant) which just does a temporal inference to the LLM with just the question the user asked, absolutely nothing else added to the prompt.

- Expert and no files only for auto-research currently is broken, as when 0 files it skips over auto-research. Fix this.
│22:21:26|Auto-research mode is 'ExpertAndNoFilesOnly'. Standard auto-research decision process is skipped. Resear││                           │
│         ch may be initiated by the expert model.                                                                ││                           │
│22:21:26|Initial auto-research phase outcome: Auto-research is configured for 'ExpertAndNoFilesOnly' mode. Resear││                           │
│         ch may be initiated by the expert model if configured.                                                  ││                           │

- We now want to implement A sound system into the application. In the config we want a `sounds_master_volume` that defaults to 25 (out of max 100, and min 0, where any values outside of the range are capped to min or max closest) and a `submit_sound` which can be either true/false/`<path>` (defaults false) where if its true it uses a unix-default included confirmation sound akin to bell but nicer if there is one, false it doesnt play at all, and if a path is supplied it attempts to play The file supplied at the path as a music file (mp3/wav/ogg/etc.). Then specifically for the submit sound we want to hook this up into interactive so that whenever is submitted weather by the user pressing enter or when an input is processed from the HTTP server then we play the submit sound using the master volume. Figure out the best cratesThat would be good to use for thisThat is not too largeAnd provides us both with playing many different audio typesAnd specifying a volume.

- add `old_code` field to applied edits, and then for `/continue` command, include both the old/new code for the previous task. (also need to think about storing all of the edits across retries of the last task in a clean way ideally)

- For summary block processing for both the editing plan summary and the edits summary, if there are 0 properly formatted blocks found, then just remove any lines that start with triple back ticks "
```
" and use the LLM's response as is after that.
- If a command is submitted via http api, then we should first clear the input box before processing it.

- implement `analysis_model` and `auto_analysis` (true/false) and `max_analysis_retries`, where after auto-test succeeds if its set to true then we inference the LLM (if using default model then full prompt cache history, else just limited to required information in small prompt) with a new analysis prompt where we first try to call git via bash in working directory and fetch the diffs of all files (if git not available at runtime then just take the edits from this latest task to keep simple), and then construct a prompt where we fetch each file that was edited (with edits applied) and put them at the start of the prompt in a first message (skip this if using default model) and then we add all of the diffs that we fetched earlier, and then we ask the LLM if after analyzing all of the diffs have these edits successfully achieved the user's task (insert user message for task). If so then simply respond with 1 sentence confirming it is sufficient, otherwise provide an `issues` block that in 1-5 paragraphs explains in detail what is missing or wrongly implemented compared to what the user has asked so that another AI can take over with planning and implementation (the focus is fully on analyzing and uncovering issues, and giving solutions). Unless the user specified otherwise, small quality of life/cleanliness/formatting/standardizing problems, or other such things which are nice to have but don't impede on effectiveness of the implementation do not count as issues. Only focus on whether the code implements the task the user specified and will run as required (the code has already passed syntax checking/compliation/etc.)

- later add `/analyze-latest-task` as a form of auto-continue that takes the previous task initial message and re-runs the analysis prompt with the analysis model.

- probably fixed, when parsing `range-replace` blocks returned from expert-editing and rust syntax fixing, if we run into any parsing errors on the blocks, then ideally for the fixing retry inferences we should first fully remove all properly parsed blocks from the original llm response and only leave all of the other text/malformed blocks in the string which will be used for the re-inference to ask for fix. Figure out the best way to implement this in a clean way that fits in with existing flows as best as possible

- Probably fixed, Delimiter fix inferencing full prompt when using non-default model for some reason includes more content than just the specific codeblock to fix. LLM has responded with fixes for *ALL* range-replace blocks provided, which means that for some reason previous messages were included and sent to the LLM even though it is non default and it should only get the delimiter-related prompt to fix (very small/precise to keep it clean and simple). This might be a problem that delimiter fixing first inference vs. its retries are different potentially (this was a problem previously, not sure if fixed) which they should be unified if they are separate and we should just go straight into retry #1 without anything before, and make sure its just the required prompt and nothing else (if using non-default model, meaning we dont need to maintain prompt cache. if default model then maintain the history for prompt caching of course). Figure out this issue after thinking hard, and fix.

- Probably fixed, needs to be checked in practice: When research auto-decision is inferenced, whether true or forced whether default/decision/expert model, neither the prompt nor the response from the LLM should be included in the message history. Right now they are and are making the history more confusing than they need to be for the LLM.

- First forced then true/false option seem to be wrong. They apply to both the first try of the task + the first retry, while we only want it to work on the first try when retries = 0.

- Check and make sure creation of new files automatically creates new folders as well if folder doesnt exist.

- Optimization:  for standard editing flow, potentially have a temporal inference step that only triggers if the user's input text is over 750 characters, where it summarizes the user's goal down to 1-3 lines, and then that is used when asking LLM for each `replace` block edit in the prompt.

- for llm inferencing request code, if a request fails, automatically retry up to x times with 1 second of wait in between which doubles each failure. the number of times this is allowed to happen is controlled by a new config value called `http_request_max_retries` which has a default of 3, and users can set to whatever. Whenever a request fails and we intend to retry a request, we debug log `HTTP request failed. Waiting and resending prompt to LLM again in N seconds.`, and if we hit max retries and we are done `HTTP request failed. Hit max number of retries. Continuing without receiving response from LLM.`

- for task summary, if there are no previous tasks then have the prompt be like it is right now. If retries > 0 and task summary starts, then update the prompt so it explicitly only summarizes the errors in the latest auto-test and the edits that have been made since.
- also for summary block if the summary block has no closing but does start, just take the whole message and add a closing block for summary automatically to avoid wasteful reprompting the llm again to fix (should still happen if we dont detect summary block opening at all)

- If retries > 0 (meaning we are in a retry) and asking the LLM whether it wants to research or not, in the prompt if retries > 0 tell the LLM that if there are auto-test errors which failed because incorrect varibles/structs/types/functions/methods/classes/traits/etc. were used, then it is highly highly recommended that the LLM returns a `research` block to have the assistant find the relevant files which hold the definitions.

- try replacing 0-0 in range blocks with `new` keyword instead for creating new files to see if less problems for llms

- Think about potentially a "patch file" approach to expert editing, where you take the response of the expert and if retries are required, then you go through a standard editing flow to replace lines in the failed to be parsed patch file to get it to a point where its valid and can be applied. Might be worth later on to see if its more effective.

- See if cancelling task/closing lledit, if possible to more cleanly kill http request so llama.cpp server stops

- finish inference crate integration and get it fully working

- Use `RUSTC_BOOTSTRAP=1 RUSTDOCFLAGS="-Z unstable-options --output-format json" cargo doc --no-deps` +  <https://github.com/tqwewe/rustdoc-md>,
and then implement a custom `markdown_doc_generator.rs` which implements logic that manually goes through all the generated .json doc files in `target/doc` and then creates a `target/doc_md` folder, where for each json file it creates a folder
named the same as the json file, and then creates a series of smaller markdown files based on the top level heading from the output generated markdown via the crate. The end goal is to have a nice structure similar to the original html from `cargo doc` but all content in markdown form.
- Create a new folder called `programming-language-integrations` with a `rust` folder inside, and create an initial `detection.rs` and `prompts.rs`
- In these add custom function to check if we detect its a rust project and working dir is at the project root (Cargo.toml in working directory)
- The implement function that will add to auto/research prompt that if it detects a `Cargo.toml` in the working directory then it returns a Some() and the contents of cargo.toml get included in the auto/research planing prompt.
- Also implement a detection func and prompt func each, which if it detects that `/target/doc` exists and is not empty, then tell the LLM in the planning prompt that `This rust project generated docs in the 'target/doc' folder for all of the crates it is using. If you require finding up-to-date info on latest function signatures, or how certain structs/traits/functions work and integrate together, researching into the doc folder is recommended.`

- think about the full implications of having pruned/summarized tasks be part of task info. Does that make sense to the LLM if say task 1/2 are pruned and put in task info, but task 3/4 are not pruned yet? Will the tasks 3/4 message history be earlier chronologically for the LLM than 1/2 in that case? Also it may not make the most sense to have it in task info as some users may want to clear task info and add their own info to it at any point. Potentially come up with better solution of holding the original Task structs in history, just cut the message history in it to just be the summaries maybe?

- Implement some UI for denoting how many previous tasks are part of the conversation history & then add a command to clear conversation history. (think how to chain this all with summarizer/relevance)
- implement `/task-history-clear` `/qa-history-clear` and `/all-history-clear` which clears both. Make sure task info is maintained even when any of these are cleared.

- if a request to LLM fails then we should retry 5 times, doubling the time in between each attempt twice, first instant retry, then 2s, 4s, etc.
- if a request takes longer than 30 minutes it should time out and retry

- Update all retries across the project from 3 to 15.
- After auto-research is finished, ask the LLM again if it has all the files it needs, or if it wants to ask a research assistant to find more files (again).

- At the end of editing flow, do a temporal inference that lists all of the file paths of files we have in context and asks the LLM which files are not relevant to the task at hand and don't provide any useful context.
  Then we parse that list, and somewhere in application state we keep a list called `to_be_pruned_files` and replace that list every time with the list of files that the LLM provided.
  Then whenever we prune the old tasks and replace them with summaries, we also implement this file pruning logic which removes from context any files which are still part of context which are on the to be pruned list (and then clear the to be pruned list).

- Also design/implement a "Relevance weighting" system where at the end of edits you ask the LLM to list any files it deemed irrelevant and maintain that weighting in the back.
Then when context size is hit, first fill in old prompts with summaries, and if still doesnt fit then do JIT file selection trimming based on irrelevance weighting from previous messages.
(think this through more for best solution later on)

- later on go back and fix the `range` block parsing as it seems to be overly strict and failing on valid blocks:

``
**Action Plan:**

- Adjust the logic in `refresh_config` to correctly merge configurations without relying on default values for CLI arguments.

### Detailed Ranges

#### 1. Verify and Correct Declaration of `refresh_config`

Ensure `refresh_config` is defined and exported properly.
```range
src/config/app_config.rs
678-690
```
2. Implement Default Trait for Args

Implement the Default trait for Args.
```range
src/cli.rs
18-30
```
Range

3. Adjust Logic in refresh_config

Ensure that refresh_config correctly merges configurations.
```range
src/config/app_config.rs
679-682
```

Thus these are all of the edits ...
``        - in expert editing flow, after retry parsing we end up with all edits duplicated. This may be that either the LLM returned all of them again including the malformed one, or that we add them twice to the list of edits, or something else. In any case we must perform de-duplication/preferably not allow edits for existing ranges to be added if that range already has an edit that was successfully parsed. Figure out why and fix.

      -  │23:14:40|Expert response contains malformed or non-block text. 2 blocks successfully parsed. Attempting to fix remaining text...                                                                                         
      We should not retry if there is non-blcok text, that is expected. We should only retry when there are clearly malformed blocks, such as blocks that are not closed at the end, blocks with invalid internal structure/content that does not match what is required, etc.


            We are working on making rust delimiter fixing really robust. Specifically:
            - if after 3 retries delimiter checking fails for a specific edit, the edit thus manages to pass over delimiter checking currently, and this causes issues as in the next loop the wrong delimiters is the standard to check new edits against which will break consistently. As such we must completely "fail" edits that fail all retries of delimiter checking and not apply the edit at all by just skipping over it (if delimiter checking not turned on/not rust file, obviously this logic gets skipped). 
            - There is also a similar problem in general if starting a task from code with broken delimiters already baked in, running into the same problem that fixing broken delimiter code will keep failing the delimiter checks as the LLM tries to actually fix the count. Thus to solve this we copy a bit from the syntax checking flow of using syn. Where before doing any delimiter checking of any edits for a given file, first syn parse the code in the file if its rust and delimiter checking is on, and if it errors with specifically "cannot parse string into token stream" that means delimiters are broken already in that file (we have verified this is the case 100%), and in that case dont do delimiter checking/fixing inferencing at all for that file during this task and just apply the edit. This should be done on every retry of course. This will thus only trigger delimiter checking when the code that is  having edits applied on is already in a good state.
            Think really hard about both of these points and write a detailed plan for each and implement

- update retries from 3x -> 5x

- implement a `restore_previous_session_models` in file config where if its set to false, then dont load any of the models (default, expert, retry, etc.) from the last session, only load the rest of the previous session. It should default to false in the config.

- replace `/ask-no-history*` commands with `/ask-no-context`  (including task info variant) which just does a temporal inference to the LLM with just the question the user asked, absolutely nothing else added to the prompt.

- Expert and no files only for auto-research currently is broken, as when 0 files it skips over auto-research. Fix this.
│22:21:26|Auto-research mode is 'ExpertAndNoFilesOnly'. Standard auto-research decision process is skipped. Resear││                           │
│         ch may be initiated by the expert model.                                                                ││                           │
│22:21:26|Initial auto-research phase outcome: Auto-research is configured for 'ExpertAndNoFilesOnly' mode. Resear││                           │
│         ch may be initiated by the expert model if configured.                                                  ││                           │

- We now want to implement A sound system into the application. In the config we want a `sounds_master_volume` that defaults to 25 (out of max 100, and min 0, where any values outside of the range are capped to min or max closest) and a `submit_sound` which can be either true/false/`<path>` (defaults false) where if its true it uses a unix-default included confirmation sound akin to bell but nicer if there is one, false it doesnt play at all, and if a path is supplied it attempts to play The file supplied at the path as a music file (mp3/wav/ogg/etc.). Then specifically for the submit sound we want to hook this up into interactive so that whenever is submitted weather by the user pressing enter or when an input is processed from the HTTP server then we play the submit sound using the master volume. Figure out the best cratesThat would be good to use for thisThat is not too largeAnd provides us both with playing many different audio typesAnd specifying a volume.

- add `old_code` field to applied edits, and then for `/continue` command, include both the old/new code for the previous task. (also need to think about storing all of the edits across retries of the last task in a clean way ideally)

- For summary block processing for both the editing plan summary and the edits summary, if there are 0 properly formatted blocks found, then just remove any lines that start with triple back ticks "```
" and use the LLM's response as is after that.
- If a command is submitted via http api, then we should first clear the input box before processing it.

- implement `analysis_model` and `auto_analysis` (true/false) and `max_analysis_retries`, where after auto-test succeeds if its set to true then we inference the LLM (if using default model then full prompt cache history, else just limited to required information in small prompt) with a new analysis prompt where we first try to call git via bash in working directory and fetch the diffs of all files (if git not available at runtime then just take the edits from this latest task to keep simple), and then construct a prompt where we fetch each file that was edited (with edits applied) and put them at the start of the prompt in a first message (skip this if using default model) and then we add all of the diffs that we fetched earlier, and then we ask the LLM if after analyzing all of the diffs have these edits successfully achieved the user's task (insert user message for task). If so then simply respond with 1 sentence confirming it is sufficient, otherwise provide an `issues` block that in 1-5 paragraphs explains in detail what is missing or wrongly implemented compared to what the user has asked so that another AI can take over with planning and implementation (the focus is fully on analyzing and uncovering issues, and giving solutions). Unless the user specified otherwise, small quality of life/cleanliness/formatting/standardizing problems, or other such things which are nice to have but don't impede on effectiveness of the implementation do not count as issues. Only focus on whether the code implements the task the user specified and will run as required (the code has already passed syntax checking/compliation/etc.)

- later add `/analyze-latest-task` as a form of auto-continue that takes the previous task initial message and re-runs the analysis prompt with the analysis model.

- probably fixed, when parsing `range-replace` blocks returned from expert-editing and rust syntax fixing, if we run into any parsing errors on the blocks, then ideally for the fixing retry inferences we should first fully remove all properly parsed blocks from the original llm response and only leave all of the other text/malformed blocks in the string which will be used for the re-inference to ask for fix. Figure out the best way to implement this in a clean way that fits in with existing flows as best as possible

- Probably fixed, Delimiter fix inferencing full prompt when using non-default model for some reason includes more content than just the specific codeblock to fix. LLM has responded with fixes for *ALL* range-replace blocks provided, which means that for some reason previous messages were included and sent to the LLM even though it is non default and it should only get the delimiter-related prompt to fix (very small/precise to keep it clean and simple). This might be a problem that delimiter fixing first inference vs. its retries are different potentially (this was a problem previously, not sure if fixed) which they should be unified if they are separate and we should just go straight into retry #1 without anything before, and make sure its just the required prompt and nothing else (if using non-default model, meaning we dont need to maintain prompt cache. if default model then maintain the history for prompt caching of course). Figure out this issue after thinking hard, and fix.

- Probably fixed, needs to be checked in practice: When research auto-decision is inferenced, whether true or forced whether default/decision/expert model, neither the prompt nor the response from the LLM should be included in the message history. Right now they are and are making the history more confusing than they need to be for the LLM.

- First forced then true/false option seem to be wrong. They apply to both the first try of the task + the first retry, while we only want it to work on the first try when retries = 0.

- Check and make sure creation of new files automatically creates new folders as well if folder doesnt exist.

- Optimization:  for standard editing flow, potentially have a temporal inference step that only triggers if the user's input text is over 750 characters, where it summarizes the user's goal down to 1-3 lines, and then that is used when asking LLM for each `replace` block edit in the prompt.

- for llm inferencing request code, if a request fails, automatically retry up to x times with 1 second of wait in between which doubles each failure. the number of times this is allowed to happen is controlled by a new config value called `http_request_max_retries` which has a default of 3, and users can set to whatever. Whenever a request fails and we intend to retry a request, we debug log `HTTP request failed. Waiting and resending prompt to LLM again in N seconds.`, and if we hit max retries and we are done `HTTP request failed. Hit max number of retries. Continuing without receiving response from LLM.`

- for task summary, if there are no previous tasks then have the prompt be like it is right now. If retries > 0 and task summary starts, then update the prompt so it explicitly only summarizes the errors in the latest auto-test and the edits that have been made since.
- also for summary block if the summary block has no closing but does start, just take the whole message and add a closing block for summary automatically to avoid wasteful reprompting the llm again to fix (should still happen if we dont detect summary block opening at all)

- If retries > 0 (meaning we are in a retry) and asking the LLM whether it wants to research or not, in the prompt if retries > 0 tell the LLM that if there are auto-test errors which failed because incorrect varibles/structs/types/functions/methods/classes/traits/etc. were used, then it is highly highly recommended that the LLM returns a `research` block to have the assistant find the relevant files which hold the definitions.

- try replacing 0-0 in range blocks with `new` keyword instead for creating new files to see if less problems for llms

- Think about potentially a "patch file" approach to expert editing, where you take the response of the expert and if retries are required, then you go through a standard editing flow to replace lines in the failed to be parsed patch file to get it to a point where its valid and can be applied. Might be worth later on to see if its more effective.

- See if cancelling task/closing lledit, if possible to more cleanly kill http request so llama.cpp server stops

- finish inference crate integration and get it fully working

- Use `RUSTC_BOOTSTRAP=1 RUSTDOCFLAGS="-Z unstable-options --output-format json" cargo doc --no-deps` +  <https://github.com/tqwewe/rustdoc-md>,
and then implement a custom `markdown_doc_generator.rs` which implements logic that manually goes through all the generated .json doc files in `target/doc` and then creates a `target/doc_md` folder, where for each json file it creates a folder
named the same as the json file, and then creates a series of smaller markdown files based on the top level heading from the output generated markdown via the crate. The end goal is to have a nice structure similar to the original html from `cargo doc` but all content in markdown form.
- Create a new folder called `programming-language-integrations` with a `rust` folder inside, and create an initial `detection.rs` and `prompts.rs`
- In these add custom function to check if we detect its a rust project and working dir is at the project root (Cargo.toml in working directory)
- The implement function that will add to auto/research prompt that if it detects a `Cargo.toml` in the working directory then it returns a Some() and the contents of cargo.toml get included in the auto/research planing prompt.
- Also implement a detection func and prompt func each, which if it detects that `/target/doc` exists and is not empty, then tell the LLM in the planning prompt that `This rust project generated docs in the 'target/doc' folder for all of the crates it is using. If you require finding up-to-date info on latest function signatures, or how certain structs/traits/functions work and integrate together, researching into the doc folder is recommended.`

- think about the full implications of having pruned/summarized tasks be part of task info. Does that make sense to the LLM if say task 1/2 are pruned and put in task info, but task 3/4 are not pruned yet? Will the tasks 3/4 message history be earlier chronologically for the LLM than 1/2 in that case? Also it may not make the most sense to have it in task info as some users may want to clear task info and add their own info to it at any point. Potentially come up with better solution of holding the original Task structs in history, just cut the message history in it to just be the summaries maybe?

- Implement some UI for denoting how many previous tasks are part of the conversation history & then add a command to clear conversation history. (think how to chain this all with summarizer/relevance)
- implement `/task-history-clear` `/qa-history-clear` and `/all-history-clear` which clears both. Make sure task info is maintained even when any of these are cleared.

- if a request to LLM fails then we should retry 5 times, doubling the time in between each attempt twice, first instant retry, then 2s, 4s, etc.
- if a request takes longer than 30 minutes it should time out and retry

- Update all retries across the project from 3 to 15.
- After auto-research is finished, ask the LLM again if it has all the files it needs, or if it wants to ask a research assistant to find more files (again).

- At the end of editing flow, do a temporal inference that lists all of the file paths of files we have in context and asks the LLM which files are not relevant to the task at hand and don't provide any useful context.
  Then we parse that list, and somewhere in application state we keep a list called `to_be_pruned_files` and replace that list every time with the list of files that the LLM provided.
  Then whenever we prune the old tasks and replace them with summaries, we also implement this file pruning logic which removes from context any files which are still part of context which are on the to be pruned list (and then clear the to be pruned list).

- Also design/implement a "Relevance weighting" system where at the end of edits you ask the LLM to list any files it deemed irrelevant and maintain that weighting in the back.
Then when context size is hit, first fill in old prompts with summaries, and if still doesnt fit then do JIT file selection trimming based on irrelevance weighting from previous messages.
(think this through more for best solution later on)

- later on go back and fix the `range` block parsing as it seems to be overly strict and failing on valid blocks:

``
**Action Plan:**

- Adjust the logic in `refresh_config` to correctly merge configurations without relying on default values for CLI arguments.

### Detailed Ranges

#### 1. Verify and Correct Declaration of `refresh_config`

Ensure `refresh_config` is defined and exported properly.
```range
src/config/app_config.rs
678-690
```
2. Implement Default Trait for Args

Implement the Default trait for Args.
```range
src/cli.rs
18-30
```
Range

3. Adjust Logic in refresh_config

Ensure that refresh_config correctly merges configurations.
```range
src/config/app_config.rs
679-682
```

Thus these are all of the edits ...
``        - in expert editing flow, after retry parsing we end up with all edits duplicated. This may be that either the LLM returned all of them again including the malformed one, or that we add them twice to the list of edits, or something else. In any case we must perform de-duplication/preferably not allow edits for existing ranges to be added if that range already has an edit that was successfully parsed. Figure out why and fix.

      -  │23:14:40|Expert response contains malformed or non-block text. 2 blocks successfully parsed. Attempting to fix remaining text...                                                                                         
      We should not retry if there is non-blcok text, that is expected. We should only retry when there are clearly malformed blocks, such as blocks that are not closed at the end, blocks with invalid internal structure/content that does not match what is required, etc.


            We are working on making rust delimiter fixing really robust. Specifically:
            - if after 3 retries delimiter checking fails for a specific edit, the edit thus manages to pass over delimiter checking currently, and this causes issues as in the next loop the wrong delimiters is the standard to check new edits against which will break consistently. As such we must completely "fail" edits that fail all retries of delimiter checking and not apply the edit at all by just skipping over it (if delimiter checking not turned on/not rust file, obviously this logic gets skipped). 
            - There is also a similar problem in general if starting a task from code with broken delimiters already baked in, running into the same problem that fixing broken delimiter code will keep failing the delimiter checks as the LLM tries to actually fix the count. Thus to solve this we copy a bit from the syntax checking flow of using syn. Where before doing any delimiter checking of any edits for a given file, first syn parse the code in the file if its rust and delimiter checking is on, and if it errors with specifically "cannot parse string into token stream" that means delimiters are broken already in that file (we have verified this is the case 100%), and in that case dont do delimiter checking/fixing inferencing at all for that file during this task and just apply the edit. This should be done on every retry of course. This will thus only trigger delimiter checking when the code that is  having edits applied on is already in a good state.
            Think really hard about both of these points and write a detailed plan for each and implement

- update retries from 3x -> 5x

- implement a `restore_previous_session_models` in file config where if its set to false, then dont load any of the models (default, expert, retry, etc.) from the last session, only load the rest of the previous session. It should default to false in the config.

- replace `/ask-no-history*` commands with `/ask-no-context`  (including task info variant) which just does a temporal inference to the LLM with just the question the user asked, absolutely nothing else added to the prompt.

- Expert and no files only for auto-research currently is broken, as when 0 files it skips over auto-research. Fix this.
│22:21:26|Auto-research mode is 'ExpertAndNoFilesOnly'. Standard auto-research decision process is skipped. Resear││                           │
│         ch may be initiated by the expert model.                                                                ││                           │
│22:21:26|Initial auto-research phase outcome: Auto-research is configured for 'ExpertAndNoFilesOnly' mode. Resear││                           │
│         ch may be initiated by the expert model if configured.                                                  ││                           │

- We now want to implement A sound system into the application. In the config we want a `sounds_master_volume` that defaults to 25 (out of max 100, and min 0, where any values outside of the range are capped to min or max closest) and a `submit_sound` which can be either true/false/`<path>` (defaults false) where if its true it uses a unix-default included confirmation sound akin to bell but nicer if there is one, false it doesnt play at all, and if a path is supplied it attempts to play The file supplied at the path as a music file (mp3/wav/ogg/etc.). Then specifically for the submit sound we want to hook this up into interactive so that whenever is submitted weather by the user pressing enter or when an input is processed from the HTTP server then we play the submit sound using the master volume. Figure out the best cratesThat would be good to use for thisThat is not too largeAnd provides us both with playing many different audio typesAnd specifying a volume.

- add `old_code` field to applied edits, and then for `/continue` command, include both the old/new code for the previous task. (also need to think about storing all of the edits across retries of the last task in a clean way ideally)

- For summary block processing for both the editing plan summary and the edits summary, if there are 0 properly formatted blocks found, then just remove any lines that start with triple back ticks "```
" and use the LLM's response as is after that.
- If a command is submitted via http api, then we should first clear the input box before processing it.

- implement `analysis_model` and `auto_analysis` (true/false) and `max_analysis_retries`, where after auto-test succeeds if its set to true then we inference the LLM (if using default model then full prompt cache history, else just limited to required information in small prompt) with a new analysis prompt where we first try to call git via bash in working directory and fetch the diffs of all files (if git not available at runtime then just take the edits from this latest task to keep simple), and then construct a prompt where we fetch each file that was edited (with edits applied) and put them at the start of the prompt in a first message (skip this if using default model) and then we add all of the diffs that we fetched earlier, and then we ask the LLM if after analyzing all of the diffs have these edits successfully achieved the user's task (insert user message for task). If so then simply respond with 1 sentence confirming it is sufficient, otherwise provide an `issues` block that in 1-5 paragraphs explains in detail what is missing or wrongly implemented compared to what the user has asked so that another AI can take over with planning and implementation (the focus is fully on analyzing and uncovering issues, and giving solutions). Unless the user specified otherwise, small quality of life/cleanliness/formatting/standardizing problems, or other such things which are nice to have but don't impede on effectiveness of the implementation do not count as issues. Only focus on whether the code implements the task the user specified and will run as required (the code has already passed syntax checking/compliation/etc.)

- later add `/analyze-latest-task` as a form of auto-continue that takes the previous task initial message and re-runs the analysis prompt with the analysis model.

- probably fixed, when parsing `range-replace` blocks returned from expert-editing and rust syntax fixing, if we run into any parsing errors on the blocks, then ideally for the fixing retry inferences we should first fully remove all properly parsed blocks from the original llm response and only leave all of the other text/malformed blocks in the string which will be used for the re-inference to ask for fix. Figure out the best way to implement this in a clean way that fits in with existing flows as best as possible

- Probably fixed, Delimiter fix inferencing full prompt when using non-default model for some reason includes more content than just the specific codeblock to fix. LLM has responded with fixes for *ALL* range-replace blocks provided, which means that for some reason previous messages were included and sent to the LLM even though it is non default and it should only get the delimiter-related prompt to fix (very small/precise to keep it clean and simple). This might be a problem that delimiter fixing first inference vs. its retries are different potentially (this was a problem previously, not sure if fixed) which they should be unified if they are separate and we should just go straight into retry #1 without anything before, and make sure its just the required prompt and nothing else (if using non-default model, meaning we dont need to maintain prompt cache. if default model then maintain the history for prompt caching of course). Figure out this issue after thinking hard, and fix.

- Probably fixed, needs to be checked in practice: When research auto-decision is inferenced, whether true or forced whether default/decision/expert model, neither the prompt nor the response from the LLM should be included in the message history. Right now they are and are making the history more confusing than they need to be for the LLM.

- First forced then true/false option seem to be wrong. They apply to both the first try of the task + the first retry, while we only want it to work on the first try when retries = 0.

- Check and make sure creation of new files automatically creates new folders as well if folder doesnt exist.

- Optimization:  for standard editing flow, potentially have a temporal inference step that only triggers if the user's input text is over 750 characters, where it summarizes the user's goal down to 1-3 lines, and then that is used when asking LLM for each `replace` block edit in the prompt.

- for llm inferencing request code, if a request fails, automatically retry up to x times with 1 second of wait in between which doubles each failure. the number of times this is allowed to happen is controlled by a new config value called `http_request_max_retries` which has a default of 3, and users can set to whatever. Whenever a request fails and we intend to retry a request, we debug log `HTTP request failed. Waiting and resending prompt to LLM again in N seconds.`, and if we hit max retries and we are done `HTTP request failed. Hit max number of retries. Continuing without receiving response from LLM.`

- for task summary, if there are no previous tasks then have the prompt be like it is right now. If retries > 0 and task summary starts, then update the prompt so it explicitly only summarizes the errors in the latest auto-test and the edits that have been made since.
- also for summary block if the summary block has no closing but does start, just take the whole message and add a closing block for summary automatically to avoid wasteful reprompting the llm again to fix (should still happen if we dont detect summary block opening at all)

- If retries > 0 (meaning we are in a retry) and asking the LLM whether it wants to research or not, in the prompt if retries > 0 tell the LLM that if there are auto-test errors which failed because incorrect varibles/structs/types/functions/methods/classes/traits/etc. were used, then it is highly highly recommended that the LLM returns a `research` block to have the assistant find the relevant files which hold the definitions.

- try replacing 0-0 in range blocks with `new` keyword instead for creating new files to see if less problems for llms

- Think about potentially a "patch file" approach to expert editing, where you take the response of the expert and if retries are required, then you go through a standard editing flow to replace lines in the failed to be parsed patch file to get it to a point where its valid and can be applied. Might be worth later on to see if its more effective.

- See if cancelling task/closing lledit, if possible to more cleanly kill http request so llama.cpp server stops

- finish inference crate integration and get it fully working

- Use `RUSTC_BOOTSTRAP=1 RUSTDOCFLAGS="-Z unstable-options --output-format json" cargo doc --no-deps` +  <https://github.com/tqwewe/rustdoc-md>,
and then implement a custom `markdown_doc_generator.rs` which implements logic that manually goes through all the generated .json doc files in `target/doc` and then creates a `target/doc_md` folder, where for each json file it creates a folder
named the same as the json file, and then creates a series of smaller markdown files based on the top level heading from the output generated markdown via the crate. The end goal is to have a nice structure similar to the original html from `cargo doc` but all content in markdown form.
- Create a new folder called `programming-language-integrations` with a `rust` folder inside, and create an initial `detection.rs` and `prompts.rs`
- In these add custom function to check if we detect its a rust project and working dir is at the project root (Cargo.toml in working directory)
- The implement function that will add to auto/research prompt that if it detects a `Cargo.toml` in the working directory then it returns a Some() and the contents of cargo.toml get included in the auto/research planing prompt.
- Also implement a detection func and prompt func each, which if it detects that `/target/doc` exists and is not empty, then tell the LLM in the planning prompt that `This rust project generated docs in the 'target/doc' folder for all of the crates it is using. If you require finding up-to-date info on latest function signatures, or how certain structs/traits/functions work and integrate together, researching into the doc folder is recommended.`

- think about the full implications of having pruned/summarized tasks be part of task info. Does that make sense to the LLM if say task 1/2 are pruned and put in task info, but task 3/4 are not pruned yet? Will the tasks 3/4 message history be earlier chronologically for the LLM than 1/2 in that case? Also it may not make the most sense to have it in task info as some users may want to clear task info and add their own info to it at any point. Potentially come up with better solution of holding the original Task structs in history, just cut the message history in it to just be the summaries maybe?

- Implement some UI for denoting how many previous tasks are part of the conversation history & then add a command to clear conversation history. (think how to chain this all with summarizer/relevance)
- implement `/task-history-clear` `/qa-history-clear` and `/all-history-clear` which clears both. Make sure task info is maintained even when any of these are cleared.

- if a request to LLM fails then we should retry 5 times, doubling the time in between each attempt twice, first instant retry, then 2s, 4s, etc.
- if a request takes longer than 30 minutes it should time out and retry

- Update all retries across the project from 3 to 15.
- After auto-research is finished, ask the LLM again if it has all the files it needs, or if it wants to ask a research assistant to find more files (again).

- At the end of editing flow, do a temporal inference that lists all of the file paths of files we have in context and asks the LLM which files are not relevant to the task at hand and don't provide any useful context.
  Then we parse that list, and somewhere in application state we keep a list called `to_be_pruned_files` and replace that list every time with the list of files that the LLM provided.
  Then whenever we prune the old tasks and replace them with summaries, we also implement this file pruning logic which removes from context any files which are still part of context which are on the to be pruned list (and then clear the to be pruned list).

- Also design/implement a "Relevance weighting" system where at the end of edits you ask the LLM to list any files it deemed irrelevant and maintain that weighting in the back.
Then when context size is hit, first fill in old prompts with summaries, and if still doesnt fit then do JIT file selection trimming based on irrelevance weighting from previous messages.
(think this through more for best solution later on)

- later on go back and fix the `range` block parsing as it seems to be overly strict and failing on valid blocks:

``
**Action Plan:**

- Adjust the logic in `refresh_config` to correctly merge configurations without relying on default values for CLI arguments.

### Detailed Ranges

#### 1. Verify and Correct Declaration of `refresh_config`

Ensure `refresh_config` is defined and exported properly.
```range
src/config/app_config.rs
678-690
```
2. Implement Default Trait for Args

Implement the Default trait for Args.
```range
src/cli.rs
18-30
```
Range

3. Adjust Logic in refresh_config

Ensure that refresh_config correctly merges configurations.
```range
src/config/app_config.rs
679-682
```

Thus these are all of the edits ...
``        - in expert editing flow, after retry parsing we end up with all edits duplicated. This may be that either the LLM returned all of them again including the malformed one, or that we add them twice to the list of edits, or something else. In any case we must perform de-duplication/preferably not allow edits for existing ranges to be added if that range already has an edit that was successfully parsed. Figure out why and fix.

      -  │23:14:40|Expert response contains malformed or non-block text. 2 blocks successfully parsed. Attempting to fix remaining text...                                                                                         
      We should not retry if there is non-blcok text, that is expected. We should only retry when there are clearly malformed blocks, such as blocks that are not closed at the end, blocks with invalid internal structure/content that does not match what is required, etc.


            We are working on making rust delimiter fixing really robust. Specifically:
            - if after 3 retries delimiter checking fails for a specific edit, the edit thus manages to pass over delimiter checking currently, and this causes issues as in the next loop the wrong delimiters is the standard to check new edits against which will break consistently. As such we must completely "fail" edits that fail all retries of delimiter checking and not apply the edit at all by just skipping over it (if delimiter checking not turned on/not rust file, obviously this logic gets skipped). 
            - There is also a similar problem in general if starting a task from code with broken delimiters already baked in, running into the same problem that fixing broken delimiter code will keep failing the delimiter checks as the LLM tries to actually fix the count. Thus to solve this we copy a bit from the syntax checking flow of using syn. Where before doing any delimiter checking of any edits for a given file, first syn parse the code in the file if its rust and delimiter checking is on, and if it errors with specifically "cannot parse string into token stream" that means delimiters are broken already in that file (we have verified this is the case 100%), and in that case dont do delimiter checking/fixing inferencing at all for that file during this task and just apply the edit. This should be done on every retry of course. This will thus only trigger delimiter checking when the code that is  having edits applied on is already in a good state.
            Think really hard about both of these points and write a detailed plan for each and implement

- update retries from 3x -> 5x

- implement a `restore_previous_session_models` in file config where if its set to false, then dont load any of the models (default, expert, retry, etc.) from the last session, only load the rest of the previous session. It should default to false in the config.

- replace `/ask-no-history*` commands with `/ask-no-context`  (including task info variant) which just does a temporal inference to the LLM with just the question the user asked, absolutely nothing else added to the prompt.

- Expert and no files only for auto-research currently is broken, as when 0 files it skips over auto-research. Fix this.
│22:21:26|Auto-research mode is 'ExpertAndNoFilesOnly'. Standard auto-research decision process is skipped. Resear││                           │
│         ch may be initiated by the expert model.                                                                ││                           │
│22:21:26|Initial auto-research phase outcome: Auto-research is configured for 'ExpertAndNoFilesOnly' mode. Resear││                           │
│         ch may be initiated by the expert model if configured.                                                  ││                           │

- We now want to implement A sound system into the application. In the config we want a `sounds_master_volume` that defaults to 25 (out of max 100, and min 0, where any values outside of the range are capped to min or max closest) and a `submit_sound` which can be either true/false/`<path>` (defaults false) where if its true it uses a unix-default included confirmation sound akin to bell but nicer if there is one, false it doesnt play at all, and if a path is supplied it attempts to play The file supplied at the path as a music file (mp3/wav/ogg/etc.). Then specifically for the submit sound we want to hook this up into interactive so that whenever is submitted weather by the user pressing enter or when an input is processed from the HTTP server then we play the submit sound using the master volume. Figure out the best cratesThat would be good to use for thisThat is not too largeAnd provides us both with playing many different audio typesAnd specifying a volume.

- add `old_code` field to applied edits, and then for `/continue` command, include both the old/new code for the previous task. (also need to think about storing all of the edits across retries of the last task in a clean way ideally)

- For summary block processing for both the editing plan summary and the edits summary, if there are 0 properly formatted blocks found, then just remove any lines that start with triple back ticks "```
" and use the LLM's response as is after that.
- If a command is submitted via http api, then we should first clear the input box before processing it.

- implement `analysis_model` and `auto_analysis` (true/false) and `max_analysis_retries`, where after auto-test succeeds if its set to true then we inference the LLM (if using default model then full prompt cache history, else just limited to required information in small prompt) with a new analysis prompt where we first try to call git via bash in working directory and fetch the diffs of all files (if git not available at runtime then just take the edits from this latest task to keep simple), and then construct a prompt where we fetch each file that was edited (with edits applied) and put them at the start of the prompt in a first message (skip this if using default model) and then we add all of the diffs that we fetched earlier, and then we ask the LLM if after analyzing all of the diffs have these edits successfully achieved the user's task (insert user message for task). If so then simply respond with 1 sentence confirming it is sufficient, otherwise provide an `issues` block that in 1-5 paragraphs explains in detail what is missing or wrongly implemented compared to what the user has asked so that another AI can take over with planning and implementation (the focus is fully on analyzing and uncovering issues, and giving solutions). Unless the user specified otherwise, small quality of life/cleanliness/formatting/standardizing problems, or other such things which are nice to have but don't impede on effectiveness of the implementation do not count as issues. Only focus on whether the code implements the task the user specified and will run as required (the code has already passed syntax checking/compliation/etc.)

- later add `/analyze-latest-task` as a form of auto-continue that takes the previous task initial message and re-runs the analysis prompt with the analysis model.

- probably fixed, when parsing `range-replace` blocks returned from expert-editing and rust syntax fixing, if we run into any parsing errors on the blocks, then ideally for the fixing retry inferences we should first fully remove all properly parsed blocks from the original llm response and only leave all of the other text/malformed blocks in the string which will be used for the re-inference to ask for fix. Figure out the best way to implement this in a clean way that fits in with existing flows as best as possible

- Probably fixed, Delimiter fix inferencing full prompt when using non-default model for some reason includes more content than just the specific codeblock to fix. LLM has responded with fixes for *ALL* range-replace blocks provided, which means that for some reason previous messages were included and sent to the LLM even though it is non default and it should only get the delimiter-related prompt to fix (very small/precise to keep it clean and simple). This might be a problem that delimiter fixing first inference vs. its retries are different potentially (this was a problem previously, not sure if fixed) which they should be unified if they are separate and we should just go straight into retry #1 without anything before, and make sure its just the required prompt and nothing else (if using non-default model, meaning we dont need to maintain prompt cache. if default model then maintain the history for prompt caching of course). Figure out this issue after thinking hard, and fix.

- Probably fixed, needs to be checked in practice: When research auto-decision is inferenced, whether true or forced whether default/decision/expert model, neither the prompt nor the response from the LLM should be included in the message history. Right now they are and are making the history more confusing than they need to be for the LLM.

- First forced then true/false option seem to be wrong. They apply to both the first try of the task + the first retry, while we only want it to work on the first try when retries = 0.

- Check and make sure creation of new files automatically creates new folders as well if folder doesnt exist.

- Optimization:  for standard editing flow, potentially have a temporal inference step that only triggers if the user's input text is over 750 characters, where it summarizes the user's goal down to 1-3 lines, and then that is used when asking LLM for each `replace` block edit in the prompt.

- for llm inferencing request code, if a request fails, automatically retry up to x times with 1 second of wait in between which doubles each failure. the number of times this is allowed to happen is controlled by a new config value called `http_request_max_retries` which has a default of 3, and users can set to whatever. Whenever a request fails and we intend to retry a request, we debug log `HTTP request failed. Waiting and resending prompt to LLM again in N seconds.`, and if we hit max retries and we are done `HTTP request failed. Hit max number of retries. Continuing without receiving response from LLM.`

- for task summary, if there are no previous tasks then have the prompt be like it is right now. If retries > 0 and task summary starts, then update the prompt so it explicitly only summarizes the errors in the latest auto-test and the edits that have been made since.
- also for summary block if the summary block has no closing but does start, just take the whole message and add a closing block for summary automatically to avoid wasteful reprompting the llm again to fix (should still happen if we dont detect summary block opening at all)

- If retries > 0 (meaning we are in a retry) and asking the LLM whether it wants to research or not, in the prompt if retries > 0 tell the LLM that if there are auto-test errors which failed because incorrect varibles/structs/types/functions/methods/classes/traits/etc. were used, then it is highly highly recommended that the LLM returns a `research` block to have the assistant find the relevant files which hold the definitions.

- try replacing 0-0 in range blocks with `new` keyword instead for creating new files to see if less problems for llms

- Think about potentially a "patch file" approach to expert editing, where you take the response of the expert and if retries are required, then you go through a standard editing flow to replace lines in the failed to be parsed patch file to get it to a point where its valid and can be applied. Might be worth later on to see if its more effective.

- See if cancelling task/closing lledit, if possible to more cleanly kill http request so llama.cpp server stops

- finish inference crate integration and get it fully working

- Use `RUSTC_BOOTSTRAP=1 RUSTDOCFLAGS="-Z unstable-options --output-format json" cargo doc --no-deps` +  <https://github.com/tqwewe/rustdoc-md>,
and then implement a custom `markdown_doc_generator.rs` which implements logic that manually goes through all the generated .json doc files in `target/doc` and then creates a `target/doc_md` folder, where for each json file it creates a folder
named the same as the json file, and then creates a series of smaller markdown files based on the top level heading from the output generated markdown via the crate. The end goal is to have a nice structure similar to the original html from `cargo doc` but all content in markdown form.
- Create a new folder called `programming-language-integrations` with a `rust` folder inside, and create an initial `detection.rs` and `prompts.rs`
- In these add custom function to check if we detect its a rust project and working dir is at the project root (Cargo.toml in working directory)
- The implement function that will add to auto/research prompt that if it detects a `Cargo.toml` in the working directory then it returns a Some() and the contents of cargo.toml get included in the auto/research planing prompt.
- Also implement a detection func and prompt func each, which if it detects that `/target/doc` exists and is not empty, then tell the LLM in the planning prompt that `This rust project generated docs in the 'target/doc' folder for all of the crates it is using. If you require finding up-to-date info on latest function signatures, or how certain structs/traits/functions work and integrate together, researching into the doc folder is recommended.`

- think about the full implications of having pruned/summarized tasks be part of task info. Does that make sense to the LLM if say task 1/2 are pruned and put in task info, but task 3/4 are not pruned yet? Will the tasks 3/4 message history be earlier chronologically for the LLM than 1/2 in that case? Also it may not make the most sense to have it in task info as some users may want to clear task info and add their own info to it at any point. Potentially come up with better solution of holding the original Task structs in history, just cut the message history in it to just be the summaries maybe?

- Implement some UI for denoting how many previous tasks are part of the conversation history & then add a command to clear conversation history. (think how to chain this all with summarizer/relevance)
- implement `/task-history-clear` `/qa-history-clear` and `/all-history-clear` which clears both. Make sure task info is maintained even when any of these are cleared.

- if a request to LLM fails then we should retry 5 times, doubling the time in between each attempt twice, first instant retry, then 2s, 4s, etc.
- if a request takes longer than 30 minutes it should time out and retry

- Update all retries across the project from 3 to 15.
- After auto-research is finished, ask the LLM again if it has all the files it needs, or if it wants to ask a research assistant to find more files (again).

- At the end of editing flow, do a temporal inference that lists all of the file paths of files we have in context and asks the LLM which files are not relevant to the task at hand and don't provide any useful context.
  Then we parse that list, and somewhere in application state we keep a list called `to_be_pruned_files` and replace that list every time with the list of files that the LLM provided.
  Then whenever we prune the old tasks and replace them with summaries, we also implement this file pruning logic which removes from context any files which are still part of context which are on the to be pruned list (and then clear the to be pruned list).

- Also design/implement a "Relevance weighting" system where at the end of edits you ask the LLM to list any files it deemed irrelevant and maintain that weighting in the back.
Then when context size is hit, first fill in old prompts with summaries, and if still doesnt fit then do JIT file selection trimming based on irrelevance weighting from previous messages.
(think this through more for best solution later on)

- later on go back and fix the `range` block parsing as it seems to be overly strict and failing on valid blocks:

``
**Action Plan:**

- Adjust the logic in `refresh_config` to correctly merge configurations without relying on default values for CLI arguments.

### Detailed Ranges

#### 1. Verify and Correct Declaration of `refresh_config`

Ensure `refresh_config` is defined and exported properly.
```range
src/config/app_config.rs
678-690
```
2. Implement Default Trait for Args

Implement the Default trait for Args.
```range
src/cli.rs
18-30
```
Range

3. Adjust Logic in refresh_config

Ensure that refresh_config correctly merges configurations.
```range
src/config/app_config.rs
679-682
```
Thus these are all of the edits ...
``        - in expert editing flow, after retry parsing we end up with all edits duplicated. This may be that either the LLM returned all of them again including the malformed one, or that we add them twice to the list of edits, or something else. In any case we must perform de-duplication/preferably not allow edits for existing ranges to be added if that range already has an edit that was successfully parsed. Figure out why and fix.

      -  │23:14:40|Expert response contains malformed or non-block text. 2 blocks successfully parsed. Attempting to fix remaining text...                                                                                         
      We should not retry if there is non-blcok text, that is expected. We should only retry when there are clearly malformed blocks, such as blocks that are not closed at the end, blocks with invalid internal structure/content that does not match what is required, etc.


            We are working on making rust delimiter fixing really robust. Specifically:
            - if after 3 retries delimiter checking fails for a specific edit, the edit thus manages to pass over delimiter checking currently, and this causes issues as in the next loop the wrong delimiters is the standard to check new edits against which will break consistently. As such we must completely "fail" edits that fail all retries of delimiter checking and not apply the edit at all by just skipping over it (if delimiter checking not turned on/not rust file, obviously this logic gets skipped). 
            - There is also a similar problem in general if starting a task from code with broken delimiters already baked in, running into the same problem that fixing broken delimiter code will keep failing the delimiter checks as the LLM tries to actually fix the count. Thus to solve this we copy a bit from the syntax checking flow of using syn. Where before doing any delimiter checking of any edits for a given file, first syn parse the code in the file if its rust and delimiter checking is on, and if it errors with specifically "cannot parse string into token stream" that means delimiters are broken already in that file (we have verified this is the case 100%), and in that case dont do delimiter checking/fixing inferencing at all for that file during this task and just apply the edit. This should be done on every retry of course. This will thus only trigger delimiter checking when the code that is  having edits applied on is already in a good state.
            Think really hard about both of these points and write a detailed plan for each and implement

- update retries from 3x -> 5x

- implement a `restore_previous_session_models` in file config where if its set to false, then dont load any of the models (default, expert, retry, etc.) from the last session, only load the rest of the previous session. It should default to false in the config.

- replace `/ask-no-history*` commands with `/ask-no-context`  (including task info variant) which just does a temporal inference to the LLM with just the question the user asked, absolutely nothing else added to the prompt.

- Expert and no files only for auto-research currently is broken, as when 0 files it skips over auto-research. Fix this.
│22:21:26|Auto-research mode is 'ExpertAndNoFilesOnly'. Standard auto-research decision process is skipped. Resear││                           │
│         ch may be initiated by the expert model.                                                                ││                           │
│22:21:26|Initial auto-research phase outcome: Auto-research is configured for 'ExpertAndNoFilesOnly' mode. Resear││                           │
│         ch may be initiated by the expert model if configured.                                                  ││                           │

- We now want to implement A sound system into the application. In the config we want a `sounds_master_volume` that defaults to 25 (out of max 100, and min 0, where any values outside of the range are capped to min or max closest) and a `submit_sound` which can be either true/false/`<path>` (defaults false) where if its true it uses a unix-default included confirmation sound akin to bell but nicer if there is one, false it doesnt play at all, and if a path is supplied it attempts to play The file supplied at the path as a music file (mp3/wav/ogg/etc.). Then specifically for the submit sound we want to hook this up into interactive so that whenever is submitted weather by the user pressing enter or when an input is processed from the HTTP server then we play the submit sound using the master volume. Figure out the best cratesThat would be good to use for thisThat is not too largeAnd provides us both with playing many different audio typesAnd specifying a volume.

- add `old_code` field to applied edits, and then for `/continue` command, include both the old/new code for the previous task. (also need to think about storing all of the edits across retries of the last task in a clean way ideally)

- For summary block processing for both the editing plan summary and the edits summary, if there are 0 properly formatted blocks found, then just remove any lines that start with triple back ticks "
```" and use the LLM's response as is after that.
- If a command is submitted via http api, then we should first clear the input box before processing it.

- implement `analysis_model` and `auto_analysis` (true/false) and `max_analysis_retries`, where after auto-test succeeds if its set to true then we inference the LLM (if using default model then full prompt cache history, else just limited to required information in small prompt) with a new analysis prompt where we first try to call git via bash in working directory and fetch the diffs of all files (if git not available at runtime then just take the edits from this latest task to keep simple), and then construct a prompt where we fetch each file that was edited (with edits applied) and put them at the start of the prompt in a first message (skip this if using default model) and then we add all of the diffs that we fetched earlier, and then we ask the LLM if after analyzing all of the diffs have these edits successfully achieved the user's task (insert user message for task). If so then simply respond with 1 sentence confirming it is sufficient, otherwise provide an `issues` block that in 1-5 paragraphs explains in detail what is missing or wrongly implemented compared to what the user has asked so that another AI can take over with planning and implementation (the focus is fully on analyzing and uncovering issues, and giving solutions). Unless the user specified otherwise, small quality of life/cleanliness/formatting/standardizing problems, or other such things which are nice to have but don't impede on effectiveness of the implementation do not count as issues. Only focus on whether the code implements the task the user specified and will run as required (the code has already passed syntax checking/compliation/etc.)

- later add `/analyze-latest-task` as a form of auto-continue that takes the previous task initial message and re-runs the analysis prompt with the analysis model.

- probably fixed, when parsing `range-replace` blocks returned from expert-editing and rust syntax fixing, if we run into any parsing errors on the blocks, then ideally for the fixing retry inferences we should first fully remove all properly parsed blocks from the original llm response and only leave all of the other text/malformed blocks in the string which will be used for the re-inference to ask for fix. Figure out the best way to implement this in a clean way that fits in with existing flows as best as possible

- Probably fixed, Delimiter fix inferencing full prompt when using non-default model for some reason includes more content than just the specific codeblock to fix. LLM has responded with fixes for *ALL* range-replace blocks provided, which means that for some reason previous messages were included and sent to the LLM even though it is non default and it should only get the delimiter-related prompt to fix (very small/precise to keep it clean and simple). This might be a problem that delimiter fixing first inference vs. its retries are different potentially (this was a problem previously, not sure if fixed) which they should be unified if they are separate and we should just go straight into retry #1 without anything before, and make sure its just the required prompt and nothing else (if using non-default model, meaning we dont need to maintain prompt cache. if default model then maintain the history for prompt caching of course). Figure out this issue after thinking hard, and fix.

- Probably fixed, needs to be checked in practice: When research auto-decision is inferenced, whether true or forced whether default/decision/expert model, neither the prompt nor the response from the LLM should be included in the message history. Right now they are and are making the history more confusing than they need to be for the LLM.

- First forced then true/false option seem to be wrong. They apply to both the first try of the task + the first retry, while we only want it to work on the first try when retries = 0.

- Check and make sure creation of new files automatically creates new folders as well if folder doesnt exist.

- Optimization:  for standard editing flow, potentially have a temporal inference step that only triggers if the user's input text is over 750 characters, where it summarizes the user's goal down to 1-3 lines, and then that is used when asking LLM for each `replace` block edit in the prompt.

- for llm inferencing request code, if a request fails, automatically retry up to x times with 1 second of wait in between which doubles each failure. the number of times this is allowed to happen is controlled by a new config value called `http_request_max_retries` which has a default of 3, and users can set to whatever. Whenever a request fails and we intend to retry a request, we debug log `HTTP request failed. Waiting and resending prompt to LLM again in N seconds.`, and if we hit max retries and we are done `HTTP request failed. Hit max number of retries. Continuing without receiving response from LLM.`

- for task summary, if there are no previous tasks then have the prompt be like it is right now. If retries > 0 and task summary starts, then update the prompt so it explicitly only summarizes the errors in the latest auto-test and the edits that have been made since.
- also for summary block if the summary block has no closing but does start, just take the whole message and add a closing block for summary automatically to avoid wasteful reprompting the llm again to fix (should still happen if we dont detect summary block opening at all)

- If retries > 0 (meaning we are in a retry) and asking the LLM whether it wants to research or not, in the prompt if retries > 0 tell the LLM that if there are auto-test errors which failed because incorrect varibles/structs/types/functions/methods/classes/traits/etc. were used, then it is highly highly recommended that the LLM returns a `research` block to have the assistant find the relevant files which hold the definitions.

- try replacing 0-0 in range blocks with `new` keyword instead for creating new files to see if less problems for llms

- Think about potentially a "patch file" approach to expert editing, where you take the response of the expert and if retries are required, then you go through a standard editing flow to replace lines in the failed to be parsed patch file to get it to a point where its valid and can be applied. Might be worth later on to see if its more effective.

- See if cancelling task/closing lledit, if possible to more cleanly kill http request so llama.cpp server stops

- finish inference crate integration and get it fully working

- Use `RUSTC_BOOTSTRAP=1 RUSTDOCFLAGS="-Z unstable-options --output-format json" cargo doc --no-deps` +  <https://github.com/tqwewe/rustdoc-md>,
and then implement a custom `markdown_doc_generator.rs` which implements logic that manually goes through all the generated .json doc files in `target/doc` and then creates a `target/doc_md` folder, where for each json file it creates a folder
named the same as the json file, and then creates a series of smaller markdown files based on the top level heading from the output generated markdown via the crate. The end goal is to have a nice structure similar to the original html from `cargo doc` but all content in markdown form.
- Create a new folder called `programming-language-integrations` with a `rust` folder inside, and create an initial `detection.rs` and `prompts.rs`
- In these add custom function to check if we detect its a rust project and working dir is at the project root (Cargo.toml in working directory)
- The implement function that will add to auto/research prompt that if it detects a `Cargo.toml` in the working directory then it returns a Some() and the contents of cargo.toml get included in the auto/research planing prompt.
- Also implement a detection func and prompt func each, which if it detects that `/target/doc` exists and is not empty, then tell the LLM in the planning prompt that `This rust project generated docs in the 'target/doc' folder for all of the crates it is using. If you require finding up-to-date info on latest function signatures, or how certain structs/traits/functions work and integrate together, researching into the doc folder is recommended.`

- think about the full implications of having pruned/summarized tasks be part of task info. Does that make sense to the LLM if say task 1/2 are pruned and put in task info, but task 3/4 are not pruned yet? Will the tasks 3/4 message history be earlier chronologically for the LLM than 1/2 in that case? Also it may not make the most sense to have it in task info as some users may want to clear task info and add their own info to it at any point. Potentially come up with better solution of holding the original Task structs in history, just cut the message history in it to just be the summaries maybe?

- Implement some UI for denoting how many previous tasks are part of the conversation history & then add a command to clear conversation history. (think how to chain this all with summarizer/relevance)
- implement `/task-history-clear` `/qa-history-clear` and `/all-history-clear` which clears both. Make sure task info is maintained even when any of these are cleared.

- if a request to LLM fails then we should retry 5 times, doubling the time in between each attempt twice, first instant retry, then 2s, 4s, etc.
- if a request takes longer than 30 minutes it should time out and retry

- Update all retries across the project from 3 to 15.
- After auto-research is finished, ask the LLM again if it has all the files it needs, or if it wants to ask a research assistant to find more files (again).

- At the end of editing flow, do a temporal inference that lists all of the file paths of files we have in context and asks the LLM which files are not relevant to the task at hand and don't provide any useful context.
  Then we parse that list, and somewhere in application state we keep a list called `to_be_pruned_files` and replace that list every time with the list of files that the LLM provided.
  Then whenever we prune the old tasks and replace them with summaries, we also implement this file pruning logic which removes from context any files which are still part of context which are on the to be pruned list (and then clear the to be pruned list).

- Also design/implement a "Relevance weighting" system where at the end of edits you ask the LLM to list any files it deemed irrelevant and maintain that weighting in the back.
Then when context size is hit, first fill in old prompts with summaries, and if still doesnt fit then do JIT file selection trimming based on irrelevance weighting from previous messages.
(think this through more for best solution later on)

- later on go back and fix the `range` block parsing as it seems to be overly strict and failing on valid blocks:

``
**Action Plan:**

- Adjust the logic in `refresh_config` to correctly merge configurations without relying on default values for CLI arguments.

### Detailed Ranges

#### 1. Verify and Correct Declaration of `refresh_config`

Ensure `refresh_config` is defined and exported properly.

```range
src/config/app_config.rs
678-690
```

2. Implement Default Trait for Args

Implement the Default trait for Args.

```range
src/cli.rs
18-30
```

Range

3. Adjust Logic in refresh_config

Ensure that refresh_config correctly merges configurations.

```range
src/config/app_config.rs
679-682
```

Thus these are all of the edits ...
``        - in expert editing flow, after retry parsing we end up with all edits duplicated. This may be that either the LLM returned all of them again including the malformed one, or that we add them twice to the list of edits, or something else. In any case we must perform de-duplication/preferably not allow edits for existing ranges to be added if that range already has an edit that was successfully parsed. Figure out why and fix.

      -  │23:14:40|Expert response contains malformed or non-block text. 2 blocks successfully parsed. Attempting to fix remaining text...                                                                                         
      We should not retry if there is non-blcok text, that is expected. We should only retry when there are clearly malformed blocks, such as blocks that are not closed at the end, blocks with invalid internal structure/content that does not match what is required, etc.


            We are working on making rust delimiter fixing really robust. Specifically:
            - if after 3 retries delimiter checking fails for a specific edit, the edit thus manages to pass over delimiter checking currently, and this causes issues as in the next loop the wrong delimiters is the standard to check new edits against which will break consistently. As such we must completely "fail" edits that fail all retries of delimiter checking and not apply the edit at all by just skipping over it (if delimiter checking not turned on/not rust file, obviously this logic gets skipped). 
            - There is also a similar problem in general if starting a task from code with broken delimiters already baked in, running into the same problem that fixing broken delimiter code will keep failing the delimiter checks as the LLM tries to actually fix the count. Thus to solve this we copy a bit from the syntax checking flow of using syn. Where before doing any delimiter checking of any edits for a given file, first syn parse the code in the file if its rust and delimiter checking is on, and if it errors with specifically "cannot parse string into token stream" that means delimiters are broken already in that file (we have verified this is the case 100%), and in that case dont do delimiter checking/fixing inferencing at all for that file during this task and just apply the edit. This should be done on every retry of course. This will thus only trigger delimiter checking when the code that is  having edits applied on is already in a good state.
            Think really hard about both of these points and write a detailed plan for each and implement

- update retries from 3x -> 5x

- implement a `restore_previous_session_models` in file config where if its set to false, then dont load any of the models (default, expert, retry, etc.) from the last session, only load the rest of the previous session. It should default to false in the config.

- replace `/ask-no-history*` commands with `/ask-no-context`  (including task info variant) which just does a temporal inference to the LLM with just the question the user asked, absolutely nothing else added to the prompt.

- Expert and no files only for auto-research currently is broken, as when 0 files it skips over auto-research. Fix this.
│22:21:26|Auto-research mode is 'ExpertAndNoFilesOnly'. Standard auto-research decision process is skipped. Resear││                           │
│         ch may be initiated by the expert model.                                                                ││                           │
│22:21:26|Initial auto-research phase outcome: Auto-research is configured for 'ExpertAndNoFilesOnly' mode. Resear││                           │
│         ch may be initiated by the expert model if configured.                                                  ││                           │

- We now want to implement A sound system into the application. In the config we want a `sounds_master_volume` that defaults to 25 (out of max 100, and min 0, where any values outside of the range are capped to min or max closest) and a `submit_sound` which can be either true/false/`<path>` (defaults false) where if its true it uses a unix-default included confirmation sound akin to bell but nicer if there is one, false it doesnt play at all, and if a path is supplied it attempts to play The file supplied at the path as a music file (mp3/wav/ogg/etc.). Then specifically for the submit sound we want to hook this up into interactive so that whenever is submitted weather by the user pressing enter or when an input is processed from the HTTP server then we play the submit sound using the master volume. Figure out the best cratesThat would be good to use for thisThat is not too largeAnd provides us both with playing many different audio typesAnd specifying a volume.

- add `old_code` field to applied edits, and then for `/continue` command, include both the old/new code for the previous task. (also need to think about storing all of the edits across retries of the last task in a clean way ideally)

- For summary block processing for both the editing plan summary and the edits summary, if there are 0 properly formatted blocks found, then just remove any lines that start with triple back ticks "```
" and use the LLM's response as is after that.
- If a command is submitted via http api, then we should first clear the input box before processing it.

- implement `analysis_model` and `auto_analysis` (true/false) and `max_analysis_retries`, where after auto-test succeeds if its set to true then we inference the LLM (if using default model then full prompt cache history, else just limited to required information in small prompt) with a new analysis prompt where we first try to call git via bash in working directory and fetch the diffs of all files (if git not available at runtime then just take the edits from this latest task to keep simple), and then construct a prompt where we fetch each file that was edited (with edits applied) and put them at the start of the prompt in a first message (skip this if using default model) and then we add all of the diffs that we fetched earlier, and then we ask the LLM if after analyzing all of the diffs have these edits successfully achieved the user's task (insert user message for task). If so then simply respond with 1 sentence confirming it is sufficient, otherwise provide an `issues` block that in 1-5 paragraphs explains in detail what is missing or wrongly implemented compared to what the user has asked so that another AI can take over with planning and implementation (the focus is fully on analyzing and uncovering issues, and giving solutions). Unless the user specified otherwise, small quality of life/cleanliness/formatting/standardizing problems, or other such things which are nice to have but don't impede on effectiveness of the implementation do not count as issues. Only focus on whether the code implements the task the user specified and will run as required (the code has already passed syntax checking/compliation/etc.)

- later add `/analyze-latest-task` as a form of auto-continue that takes the previous task initial message and re-runs the analysis prompt with the analysis model.

- probably fixed, when parsing `range-replace` blocks returned from expert-editing and rust syntax fixing, if we run into any parsing errors on the blocks, then ideally for the fixing retry inferences we should first fully remove all properly parsed blocks from the original llm response and only leave all of the other text/malformed blocks in the string which will be used for the re-inference to ask for fix. Figure out the best way to implement this in a clean way that fits in with existing flows as best as possible

- Probably fixed, Delimiter fix inferencing full prompt when using non-default model for some reason includes more content than just the specific codeblock to fix. LLM has responded with fixes for *ALL* range-replace blocks provided, which means that for some reason previous messages were included and sent to the LLM even though it is non default and it should only get the delimiter-related prompt to fix (very small/precise to keep it clean and simple). This might be a problem that delimiter fixing first inference vs. its retries are different potentially (this was a problem previously, not sure if fixed) which they should be unified if they are separate and we should just go straight into retry #1 without anything before, and make sure its just the required prompt and nothing else (if using non-default model, meaning we dont need to maintain prompt cache. if default model then maintain the history for prompt caching of course). Figure out this issue after thinking hard, and fix.

- Probably fixed, needs to be checked in practice: When research auto-decision is inferenced, whether true or forced whether default/decision/expert model, neither the prompt nor the response from the LLM should be included in the message history. Right now they are and are making the history more confusing than they need to be for the LLM.

- First forced then true/false option seem to be wrong. They apply to both the first try of the task + the first retry, while we only want it to work on the first try when retries = 0.

- Check and make sure creation of new files automatically creates new folders as well if folder doesnt exist.

- Optimization:  for standard editing flow, potentially have a temporal inference step that only triggers if the user's input text is over 750 characters, where it summarizes the user's goal down to 1-3 lines, and then that is used when asking LLM for each `replace` block edit in the prompt.

- for llm inferencing request code, if a request fails, automatically retry up to x times with 1 second of wait in between which doubles each failure. the number of times this is allowed to happen is controlled by a new config value called `http_request_max_retries` which has a default of 3, and users can set to whatever. Whenever a request fails and we intend to retry a request, we debug log `HTTP request failed. Waiting and resending prompt to LLM again in N seconds.`, and if we hit max retries and we are done `HTTP request failed. Hit max number of retries. Continuing without receiving response from LLM.`

- for task summary, if there are no previous tasks then have the prompt be like it is right now. If retries > 0 and task summary starts, then update the prompt so it explicitly only summarizes the errors in the latest auto-test and the edits that have been made since.
- also for summary block if the summary block has no closing but does start, just take the whole message and add a closing block for summary automatically to avoid wasteful reprompting the llm again to fix (should still happen if we dont detect summary block opening at all)

- If retries > 0 (meaning we are in a retry) and asking the LLM whether it wants to research or not, in the prompt if retries > 0 tell the LLM that if there are auto-test errors which failed because incorrect varibles/structs/types/functions/methods/classes/traits/etc. were used, then it is highly highly recommended that the LLM returns a `research` block to have the assistant find the relevant files which hold the definitions.

- try replacing 0-0 in range blocks with `new` keyword instead for creating new files to see if less problems for llms

- Think about potentially a "patch file" approach to expert editing, where you take the response of the expert and if retries are required, then you go through a standard editing flow to replace lines in the failed to be parsed patch file to get it to a point where its valid and can be applied. Might be worth later on to see if its more effective.

- See if cancelling task/closing lledit, if possible to more cleanly kill http request so llama.cpp server stops

- finish inference crate integration and get it fully working

- Use `RUSTC_BOOTSTRAP=1 RUSTDOCFLAGS="-Z unstable-options --output-format json" cargo doc --no-deps` +  <https://github.com/tqwewe/rustdoc-md>,
and then implement a custom `markdown_doc_generator.rs` which implements logic that manually goes through all the generated .json doc files in `target/doc` and then creates a `target/doc_md` folder, where for each json file it creates a folder
named the same as the json file, and then creates a series of smaller markdown files based on the top level heading from the output generated markdown via the crate. The end goal is to have a nice structure similar to the original html from `cargo doc` but all content in markdown form.
- Create a new folder called `programming-language-integrations` with a `rust` folder inside, and create an initial `detection.rs` and `prompts.rs`
- In these add custom function to check if we detect its a rust project and working dir is at the project root (Cargo.toml in working directory)
- The implement function that will add to auto/research prompt that if it detects a `Cargo.toml` in the working directory then it returns a Some() and the contents of cargo.toml get included in the auto/research planing prompt.
- Also implement a detection func and prompt func each, which if it detects that `/target/doc` exists and is not empty, then tell the LLM in the planning prompt that `This rust project generated docs in the 'target/doc' folder for all of the crates it is using. If you require finding up-to-date info on latest function signatures, or how certain structs/traits/functions work and integrate together, researching into the doc folder is recommended.`

- think about the full implications of having pruned/summarized tasks be part of task info. Does that make sense to the LLM if say task 1/2 are pruned and put in task info, but task 3/4 are not pruned yet? Will the tasks 3/4 message history be earlier chronologically for the LLM than 1/2 in that case? Also it may not make the most sense to have it in task info as some users may want to clear task info and add their own info to it at any point. Potentially come up with better solution of holding the original Task structs in history, just cut the message history in it to just be the summaries maybe?

- Implement some UI for denoting how many previous tasks are part of the conversation history & then add a command to clear conversation history. (think how to chain this all with summarizer/relevance)
- implement `/task-history-clear` `/qa-history-clear` and `/all-history-clear` which clears both. Make sure task info is maintained even when any of these are cleared.

- if a request to LLM fails then we should retry 5 times, doubling the time in between each attempt twice, first instant retry, then 2s, 4s, etc.
- if a request takes longer than 30 minutes it should time out and retry

- Update all retries across the project from 3 to 15.
- After auto-research is finished, ask the LLM again if it has all the files it needs, or if it wants to ask a research assistant to find more files (again).

- At the end of editing flow, do a temporal inference that lists all of the file paths of files we have in context and asks the LLM which files are not relevant to the task at hand and don't provide any useful context.
  Then we parse that list, and somewhere in application state we keep a list called `to_be_pruned_files` and replace that list every time with the list of files that the LLM provided.
  Then whenever we prune the old tasks and replace them with summaries, we also implement this file pruning logic which removes from context any files which are still part of context which are on the to be pruned list (and then clear the to be pruned list).

- Also design/implement a "Relevance weighting" system where at the end of edits you ask the LLM to list any files it deemed irrelevant and maintain that weighting in the back.
Then when context size is hit, first fill in old prompts with summaries, and if still doesnt fit then do JIT file selection trimming based on irrelevance weighting from previous messages.
(think this through more for best solution later on)

- later on go back and fix the `range` block parsing as it seems to be overly strict and failing on valid blocks:

``
**Action Plan:**

- Adjust the logic in `refresh_config` to correctly merge configurations without relying on default values for CLI arguments.

### Detailed Ranges

#### 1. Verify and Correct Declaration of `refresh_config`

Ensure `refresh_config` is defined and exported properly.
```range
src/config/app_config.rs
678-690
```
2. Implement Default Trait for Args

Implement the Default trait for Args.
```range
src/cli.rs
18-30
```
Range

3. Adjust Logic in refresh_config

Ensure that refresh_config correctly merges configurations.
```range
src/config/app_config.rs
679-682
```

Thus these are all of the edits ...
``        - in expert editing flow, after retry parsing we end up with all edits duplicated. This may be that either the LLM returned all of them again including the malformed one, or that we add them twice to the list of edits, or something else. In any case we must perform de-duplication/preferably not allow edits for existing ranges to be added if that range already has an edit that was successfully parsed. Figure out why and fix.

      -  │23:14:40|Expert response contains malformed or non-block text. 2 blocks successfully parsed. Attempting to fix remaining text...                                                                                         
      We should not retry if there is non-blcok text, that is expected. We should only retry when there are clearly malformed blocks, such as blocks that are not closed at the end, blocks with invalid internal structure/content that does not match what is required, etc.


            We are working on making rust delimiter fixing really robust. Specifically:
            - if after 3 retries delimiter checking fails for a specific edit, the edit thus manages to pass over delimiter checking currently, and this causes issues as in the next loop the wrong delimiters is the standard to check new edits against which will break consistently. As such we must completely "fail" edits that fail all retries of delimiter checking and not apply the edit at all by just skipping over it (if delimiter checking not turned on/not rust file, obviously this logic gets skipped). 
            - There is also a similar problem in general if starting a task from code with broken delimiters already baked in, running into the same problem that fixing broken delimiter code will keep failing the delimiter checks as the LLM tries to actually fix the count. Thus to solve this we copy a bit from the syntax checking flow of using syn. Where before doing any delimiter checking of any edits for a given file, first syn parse the code in the file if its rust and delimiter checking is on, and if it errors with specifically "cannot parse string into token stream" that means delimiters are broken already in that file (we have verified this is the case 100%), and in that case dont do delimiter checking/fixing inferencing at all for that file during this task and just apply the edit. This should be done on every retry of course. This will thus only trigger delimiter checking when the code that is  having edits applied on is already in a good state.
            Think really hard about both of these points and write a detailed plan for each and implement

- update retries from 3x -> 5x

- implement a `restore_previous_session_models` in file config where if its set to false, then dont load any of the models (default, expert, retry, etc.) from the last session, only load the rest of the previous session. It should default to false in the config.

- replace `/ask-no-history*` commands with `/ask-no-context`  (including task info variant) which just does a temporal inference to the LLM with just the question the user asked, absolutely nothing else added to the prompt.

- Expert and no files only for auto-research currently is broken, as when 0 files it skips over auto-research. Fix this.
│22:21:26|Auto-research mode is 'ExpertAndNoFilesOnly'. Standard auto-research decision process is skipped. Resear││                           │
│         ch may be initiated by the expert model.                                                                ││                           │
│22:21:26|Initial auto-research phase outcome: Auto-research is configured for 'ExpertAndNoFilesOnly' mode. Resear││                           │
│         ch may be initiated by the expert model if configured.                                                  ││                           │

- We now want to implement A sound system into the application. In the config we want a `sounds_master_volume` that defaults to 25 (out of max 100, and min 0, where any values outside of the range are capped to min or max closest) and a `submit_sound` which can be either true/false/`<path>` (defaults false) where if its true it uses a unix-default included confirmation sound akin to bell but nicer if there is one, false it doesnt play at all, and if a path is supplied it attempts to play The file supplied at the path as a music file (mp3/wav/ogg/etc.). Then specifically for the submit sound we want to hook this up into interactive so that whenever is submitted weather by the user pressing enter or when an input is processed from the HTTP server then we play the submit sound using the master volume. Figure out the best cratesThat would be good to use for thisThat is not too largeAnd provides us both with playing many different audio typesAnd specifying a volume.

- add `old_code` field to applied edits, and then for `/continue` command, include both the old/new code for the previous task. (also need to think about storing all of the edits across retries of the last task in a clean way ideally)

- For summary block processing for both the editing plan summary and the edits summary, if there are 0 properly formatted blocks found, then just remove any lines that start with triple back ticks "```" and use the LLM's response as is after that.
- If a command is submitted via http api, then we should first clear the input box before processing it.

- implement `analysis_model` and `auto_analysis` (true/false) and `max_analysis_retries`, where after auto-test succeeds if its set to true then we inference the LLM (if using default model then full prompt cache history, else just limited to required information in small prompt) with a new analysis prompt where we first try to call git via bash in working directory and fetch the diffs of all files (if git not available at runtime then just take the edits from this latest task to keep simple), and then construct a prompt where we fetch each file that was edited (with edits applied) and put them at the start of the prompt in a first message (skip this if using default model) and then we add all of the diffs that we fetched earlier, and then we ask the LLM if after analyzing all of the diffs have these edits successfully achieved the user's task (insert user message for task). If so then simply respond with 1 sentence confirming it is sufficient, otherwise provide an `issues` block that in 1-5 paragraphs explains in detail what is missing or wrongly implemented compared to what the user has asked so that another AI can take over with planning and implementation (the focus is fully on analyzing and uncovering issues, and giving solutions). Unless the user specified otherwise, small quality of life/cleanliness/formatting/standardizing problems, or other such things which are nice to have but don't impede on effectiveness of the implementation do not count as issues. Only focus on whether the code implements the task the user specified and will run as required (the code has already passed syntax checking/compliation/etc.)

- later add `/analyze-latest-task` as a form of auto-continue that takes the previous task initial message and re-runs the analysis prompt with the analysis model.

- probably fixed, when parsing `range-replace` blocks returned from expert-editing and rust syntax fixing, if we run into any parsing errors on the blocks, then ideally for the fixing retry inferences we should first fully remove all properly parsed blocks from the original llm response and only leave all of the other text/malformed blocks in the string which will be used for the re-inference to ask for fix. Figure out the best way to implement this in a clean way that fits in with existing flows as best as possible

- Probably fixed, Delimiter fix inferencing full prompt when using non-default model for some reason includes more content than just the specific codeblock to fix. LLM has responded with fixes for *ALL* range-replace blocks provided, which means that for some reason previous messages were included and sent to the LLM even though it is non default and it should only get the delimiter-related prompt to fix (very small/precise to keep it clean and simple). This might be a problem that delimiter fixing first inference vs. its retries are different potentially (this was a problem previously, not sure if fixed) which they should be unified if they are separate and we should just go straight into retry #1 without anything before, and make sure its just the required prompt and nothing else (if using non-default model, meaning we dont need to maintain prompt cache. if default model then maintain the history for prompt caching of course). Figure out this issue after thinking hard, and fix.

- Probably fixed, needs to be checked in practice: When research auto-decision is inferenced, whether true or forced whether default/decision/expert model, neither the prompt nor the response from the LLM should be included in the message history. Right now they are and are making the history more confusing than they need to be for the LLM.

- First forced then true/false option seem to be wrong. They apply to both the first try of the task + the first retry, while we only want it to work on the first try when retries = 0.

- Check and make sure creation of new files automatically creates new folders as well if folder doesnt exist.

- Optimization:  for standard editing flow, potentially have a temporal inference step that only triggers if the user's input text is over 750 characters, where it summarizes the user's goal down to 1-3 lines, and then that is used when asking LLM for each `replace` block edit in the prompt.

- for llm inferencing request code, if a request fails, automatically retry up to x times with 1 second of wait in between which doubles each failure. the number of times this is allowed to happen is controlled by a new config value called `http_request_max_retries` which has a default of 3, and users can set to whatever. Whenever a request fails and we intend to retry a request, we debug log `HTTP request failed. Waiting and resending prompt to LLM again in N seconds.`, and if we hit max retries and we are done `HTTP request failed. Hit max number of retries. Continuing without receiving response from LLM.`

- for task summary, if there are no previous tasks then have the prompt be like it is right now. If retries > 0 and task summary starts, then update the prompt so it explicitly only summarizes the errors in the latest auto-test and the edits that have been made since.
- also for summary block if the summary block has no closing but does start, just take the whole message and add a closing block for summary automatically to avoid wasteful reprompting the llm again to fix (should still happen if we dont detect summary block opening at all)

- If retries > 0 (meaning we are in a retry) and asking the LLM whether it wants to research or not, in the prompt if retries > 0 tell the LLM that if there are auto-test errors which failed because incorrect varibles/structs/types/functions/methods/classes/traits/etc. were used, then it is highly highly recommended that the LLM returns a `research` block to have the assistant find the relevant files which hold the definitions.

- try replacing 0-0 in range blocks with `new` keyword instead for creating new files to see if less problems for llms

- Think about potentially a "patch file" approach to expert editing, where you take the response of the expert and if retries are required, then you go through a standard editing flow to replace lines in the failed to be parsed patch file to get it to a point where its valid and can be applied. Might be worth later on to see if its more effective.

- See if cancelling task/closing lledit, if possible to more cleanly kill http request so llama.cpp server stops

- finish inference crate integration and get it fully working

- Use `RUSTC_BOOTSTRAP=1 RUSTDOCFLAGS="-Z unstable-options --output-format json" cargo doc --no-deps` +  <https://github.com/tqwewe/rustdoc-md>,
and then implement a custom `markdown_doc_generator.rs` which implements logic that manually goes through all the generated .json doc files in `target/doc` and then creates a `target/doc_md` folder, where for each json file it creates a folder
named the same as the json file, and then creates a series of smaller markdown files based on the top level heading from the output generated markdown via the crate. The end goal is to have a nice structure similar to the original html from `cargo doc` but all content in markdown form.
- Create a new folder called `programming-language-integrations` with a `rust` folder inside, and create an initial `detection.rs` and `prompts.rs`
- In these add custom function to check if we detect its a rust project and working dir is at the project root (Cargo.toml in working directory)
- The implement function that will add to auto/research prompt that if it detects a `Cargo.toml` in the working directory then it returns a Some() and the contents of cargo.toml get included in the auto/research planing prompt.
- Also implement a detection func and prompt func each, which if it detects that `/target/doc` exists and is not empty, then tell the LLM in the planning prompt that `This rust project generated docs in the 'target/doc' folder for all of the crates it is using. If you require finding up-to-date info on latest function signatures, or how certain structs/traits/functions work and integrate together, researching into the doc folder is recommended.`

- think about the full implications of having pruned/summarized tasks be part of task info. Does that make sense to the LLM if say task 1/2 are pruned and put in task info, but task 3/4 are not pruned yet? Will the tasks 3/4 message history be earlier chronologically for the LLM than 1/2 in that case? Also it may not make the most sense to have it in task info as some users may want to clear task info and add their own info to it at any point. Potentially come up with better solution of holding the original Task structs in history, just cut the message history in it to just be the summaries maybe?

- Implement some UI for denoting how many previous tasks are part of the conversation history & then add a command to clear conversation history. (think how to chain this all with summarizer/relevance)
- implement `/task-history-clear` `/qa-history-clear` and `/all-history-clear` which clears both. Make sure task info is maintained even when any of these are cleared.

- if a request to LLM fails then we should retry 5 times, doubling the time in between each attempt twice, first instant retry, then 2s, 4s, etc.
- if a request takes longer than 30 minutes it should time out and retry

- Update all retries across the project from 3 to 15.
- After auto-research is finished, ask the LLM again if it has all the files it needs, or if it wants to ask a research assistant to find more files (again).

- At the end of editing flow, do a temporal inference that lists all of the file paths of files we have in context and asks the LLM which files are not relevant to the task at hand and don't provide any useful context.
  Then we parse that list, and somewhere in application state we keep a list called `to_be_pruned_files` and replace that list every time with the list of files that the LLM provided.
  Then whenever we prune the old tasks and replace them with summaries, we also implement this file pruning logic which removes from context any files which are still part of context which are on the to be pruned list (and then clear the to be pruned list).

- Also design/implement a "Relevance weighting" system where at the end of edits you ask the LLM to list any files it deemed irrelevant and maintain that weighting in the back.
Then when context size is hit, first fill in old prompts with summaries, and if still doesnt fit then do JIT file selection trimming based on irrelevance weighting from previous messages.
(think this through more for best solution later on)

- later on go back and fix the `range` block parsing as it seems to be overly strict and failing on valid blocks:

``
**Action Plan:**

- Adjust the logic in `refresh_config` to correctly merge configurations without relying on default values for CLI arguments.

### Detailed Ranges

#### 1. Verify and Correct Declaration of `refresh_config`

Ensure `refresh_config` is defined and exported properly.

```range
src/config/app_config.rs
678-690
```

2. Implement Default Trait for Args

Implement the Default trait for Args.

```range
src/cli.rs
18-30
```

Range

3. Adjust Logic in refresh_config

Ensure that refresh_config correctly merges configurations.

```range
src/config/app_config.rs
679-682
```

Thus these are all of the edits ...
``
