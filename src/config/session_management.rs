use crate::config::app_config::{
    AppConfig, AutoExpertMode, AutoExpertSwitch, AutoResearchMode, NoThinkMode, ResultsOutputMode,
};
use crate::files::ordered_files::OrderedFiles;
use crate::result::SerializableTask;
use crate::task::Task;
use log::{debug, info, warn};
use serde::{Deserialize, Serialize};
use std::collections::HashSet;
use std::env;
use std::path::PathBuf;

pub const PREVIOUS_SESSION_FILENAME: &str = ".lledit.previous_session";

#[derive(Serialize, Deserialize, Debug, Default, Clone)]
#[serde(default)]
struct RestorableAppConfig {
    default_model: Option<String>,
    task_info: Option<String>,
    no_think: Option<HashSet<NoThinkMode>>,
    results_output_mode: Option<ResultsOutputMode>,
    results_output_file: Option<PathBuf>,
    exit_on_success: Option<bool>,
    timestamps: Option<bool>,
    auto_test_command: Option<String>,
    auto_test_toggle: Option<bool>,
    max_task_retries: Option<usize>,
    auto_research_mode: Option<AutoResearchMode>,
    auto_expert_switch: Option<AutoExpertSwitch>,
    expert_model: Option<String>,
    auto_expert_mode: Option<AutoExpertMode>,
    expert_model_auto_research_loop_max: Option<usize>,
    notification_command: Option<String>,
    decision_model_auto_research_loop_max: Option<usize>,
    retry_model: Option<String>,
    summary_model: Option<String>,
    research_model: Option<String>,
    decision_model: Option<String>,
    http_api_port: Option<u16>,
    advanced_language_features: Option<HashSet<String>>,
}

#[derive(Serialize, Deserialize, Debug, Default)]
#[serde(default)]
pub struct SessionState {
    config: RestorableAppConfig,
    tasks: Vec<SerializableTask>,
    file_paths: Vec<PathBuf>,
    #[serde(default)]
    input_history: Vec<String>,
}

pub async fn save_session(
    app_config: &AppConfig,
    tasks: &Vec<Task>,
    ordered_files: &OrderedFiles,
    input_history: &Vec<String>,
) -> Result<(), Box<dyn std::error::Error>> {
    if !app_config.restore_previous_session_on_startup {
        return Ok(());
    }

    let session_file_path = env::current_dir()?.join(PREVIOUS_SESSION_FILENAME);

    let restorable_config = RestorableAppConfig {
        default_model: Some(app_config.default_model.clone()),
        task_info: app_config.task_info.clone(),
        no_think: Some(app_config.no_think.clone()),
        results_output_mode: Some(app_config.results_output_mode),
        results_output_file: app_config.results_output_file.clone(),
        exit_on_success: Some(app_config.exit_on_success),
        timestamps: Some(app_config.timestamps),
        auto_test_command: Some(app_config.auto_test_command.clone()),
        auto_test_toggle: Some(app_config.auto_test_toggle),
        max_task_retries: Some(app_config.max_task_retries),
        auto_research_mode: Some(app_config.auto_research_mode),
        auto_expert_switch: Some(app_config.auto_expert_switch),
        expert_model: app_config.expert_model.clone(),
        auto_expert_mode: Some(app_config.auto_expert_mode),
        expert_model_auto_research_loop_max: Some(app_config.expert_model_auto_research_loop_max),
        notification_command: Some(app_config.notification_command.clone()),
        decision_model_auto_research_loop_max: Some(
            app_config.decision_model_auto_research_loop_max,
        ),
        retry_model: app_config.retry_model.clone(),
        summary_model: app_config.summary_model.clone(),
        research_model: app_config.research_model.clone(),
        decision_model: app_config.decision_model.clone(),
        http_api_port: Some(app_config.http_api_port),
        advanced_language_features: Some(app_config.advanced_language_features.clone()),
    };

    let serializable_tasks = tasks.iter().map(SerializableTask::from).collect();

    let session_state = SessionState {
        config: restorable_config,
        tasks: serializable_tasks,
        file_paths: ordered_files.get_all_labeled_files_map().keys().cloned().collect(),
        input_history: input_history.clone(),
    };

    let json_data = serde_json::to_string_pretty(&session_state)?;
    tokio::fs::write(&session_file_path, json_data).await?;
    info!("Session state saved to {}", session_file_path.display());

    Ok(())
}

pub async fn load_and_apply_session(
    app_config: &mut AppConfig,
    tasks: &mut Vec<Task>,
    ordered_files: &mut OrderedFiles,
    input_history: &mut Vec<String>,
) -> Result<(), Box<dyn std::error::Error>> {
    if !app_config.restore_previous_session_on_startup {
        return Ok(());
    }

    let session_file_path = env::current_dir()?.join(PREVIOUS_SESSION_FILENAME);

    if !session_file_path.exists() {
        debug!("No previous session file found. Starting fresh.");
        return Ok(());
    }

    let content = tokio::fs::read_to_string(&session_file_path).await?;
    let session_state: SessionState = match serde_json::from_str(&content) {
        Ok(state) => state,
        Err(e) => {
            warn!(
                "Failed to parse previous session file: {}. Starting fresh.",
                e
            );
            return Ok(());
        }
    };

    info!("Restoring state from previous session.");

    // Apply config
    let config = session_state.config;
    if let Some(val) = config.default_model {
        app_config.default_model = val;
    }
    if config.task_info.is_some() {
        app_config.task_info = config.task_info;
    }
    if let Some(val) = config.no_think {
        app_config.no_think = val;
    }
    if let Some(val) = config.results_output_mode {
        app_config.results_output_mode = val;
    }
    if config.results_output_file.is_some() {
        app_config.results_output_file = config.results_output_file;
    }
    if let Some(val) = config.exit_on_success {
        app_config.exit_on_success = val;
    }
    if let Some(val) = config.timestamps {
        app_config.timestamps = val;
    }
    if let Some(val) = config.auto_test_command {
        app_config.auto_test_command = val;
    }
    if let Some(val) = config.auto_test_toggle {
        app_config.auto_test_toggle = val;
    }
    if let Some(val) = config.max_task_retries {
        app_config.max_task_retries = val;
    }
    if let Some(val) = config.auto_research_mode {
        app_config.auto_research_mode = val;
    }
    if let Some(val) = config.auto_expert_switch {
        app_config.auto_expert_switch = val;
    }
    if config.expert_model.is_some() {
        app_config.expert_model = config.expert_model;
    }
    if let Some(val) = config.auto_expert_mode {
        app_config.auto_expert_mode = val;
    }
    if let Some(val) = config.expert_model_auto_research_loop_max {
        app_config.expert_model_auto_research_loop_max = val;
    }
    if let Some(val) = config.notification_command {
        app_config.notification_command = val;
    }
    if let Some(val) = config.decision_model_auto_research_loop_max {
        app_config.decision_model_auto_research_loop_max = val;
    }
    if config.retry_model.is_some() {
        app_config.retry_model = config.retry_model;
    }
    if config.summary_model.is_some() {
        app_config.summary_model = config.summary_model;
    }
    if config.research_model.is_some() {
        app_config.research_model = config.research_model;
    }
    if config.decision_model.is_some() {
        app_config.decision_model = config.decision_model;
    }
    if let Some(val) = config.http_api_port {
        app_config.http_api_port = val;
    }
    if let Some(val) = config.advanced_language_features {
        app_config.advanced_language_features = val;
    }

    // Apply tasks
    *tasks = session_state.tasks.into_iter().map(Task::from).collect();

    // Apply input history
    *input_history = session_state.input_history;

    // Apply file paths to OrderedFiles
    // Apply file paths to OrderedFiles
    *ordered_files = OrderedFiles::new();
    for path in session_state.file_paths {
        if let Err(e) = ordered_files.add_user_file(&path).await {
            warn!(
                "Failed to restore file from session {}: {}",
                path.display(),
                e
            );
        }
    }

    // Remove the session file after successful load to prevent accidental reloads on restart
    if let Err(e) = tokio::fs::remove_file(&session_file_path).await {
        warn!("Failed to remove session file after loading: {}", e);
    }

    Ok(())
}