#[cfg(test)]
use super::commands_utils::*;
#[cfg(test)]
use super::commands::{parse_command_from_input, handle_command_if_present, ParsedCommand};
#[cfg(test)]
use crate::config::app_config::{AutoExpertMode, AutoExpertSwitch, AutoResearchMode};
#[cfg(test)]
use crate::config::AppConfig as MainAppConfig;
#[cfg(test)]
use crate::interactive::app::InteractiveApp;
#[cfg(test)]
use crate::llm::{ChatMessage, MessageType};
#[cfg(test)]
use crate::task::Task;
#[cfg(test)]
use std::collections::HashSet;
#[cfg(test)]
use std::io::Write;
#[cfg(test)]
use tempfile::NamedTempFile;
#[cfg(test)]
use tokio::sync::mpsc;

#[cfg(test)]
fn default_test_app_config() -> MainAppConfig {
    MainAppConfig {
        user_prompt: String::new(),
        file_paths: Vec::new(),
        default_model: "test_default_model".to_string(),
        provider: "openai".to_string(),
        model: "gpt-4o".to_string(),
        effective_provider_api_key: String::new(),
        provider_url: String::new(),
        models_list: None,
        results_input_file: None,
        results_input: None,
        task_info: None,
        no_think: HashSet::new(),
        results_output_mode: crate::config::app_config::ResultsOutputMode::None,
        results_output_file: None,
        log_level: crate::logger::LogLevel::Info,
        exit_on_success: false,
        timestamps: true,
        restore_previous_session_on_startup: false,
        auto_test_command: String::new(),
        auto_test_toggle: false,
        max_task_retries: 99999,
        auto_research_mode: AutoResearchMode::True,
        auto_expert_switch: AutoExpertSwitch::False,
        expert_model: None,
        auto_expert_mode: AutoExpertMode::Planning,
        notification_command: String::new(),
        forced_research_for_next_cycle: None,
        decision_model_auto_research_loop_max: 2,
        expert_model_auto_research_loop_max: 2,
        retry_model: None,
        summary_model: None,
        research_model: None,
        decision_model: None,
        advanced_language_features: HashSet::new(),
        http_api_port: 8080,
    }
}

#[cfg(test)]
fn new_empty_app() -> InteractiveApp {
    let config = default_test_app_config();
    InteractiveApp::new(
        Vec::new(),                                    // initial_tasks
        Vec::new(),                                    // initial_qna_history
        Vec::new(),                                    // initial_input_history
        crate::files::ordered_files::OrderedFiles::new(), // initial_ordered_files
        String::new(),                                 // initial_prompt
        config                                         // base_app_config
    )
}

#[test]
fn test_parse_command_variants() {
    assert_eq!(parse_command_from_input("/exit"), ParsedCommand::Exit);
    assert_eq!(parse_command_from_input("  /exit"), ParsedCommand::Exit);
    assert_eq!(parse_command_from_input("/exit args"), ParsedCommand::Exit);
    assert_eq!(parse_command_from_input("/quit"), ParsedCommand::Exit);
    assert_eq!(parse_command_from_input("  /quit"), ParsedCommand::Exit);
    assert_eq!(parse_command_from_input("/quit args"), ParsedCommand::Exit);
    assert_eq!(
        parse_command_from_input("/add foo.txt"),
        ParsedCommand::Add("foo.txt".to_string())
    );
    assert_eq!(
        parse_command_from_input("/add path/to/file.rs"),
        ParsedCommand::Add("path/to/file.rs".to_string())
    );
    assert_eq!(
        parse_command_from_input("/add"),
        ParsedCommand::Unknown("/add: missing argument".to_string())
    );

    assert_eq!(parse_command_from_input("/pwd"), ParsedCommand::Pwd);
    assert_eq!(
        parse_command_from_input("/cd /tmp"),
        ParsedCommand::Cd("/tmp".to_string())
    );
    assert_eq!(
        parse_command_from_input("/cd project/src"),
        ParsedCommand::Cd("project/src".to_string())
    );
    assert_eq!(
        parse_command_from_input("/cd"),
        ParsedCommand::Unknown("/cd: missing argument".to_string())
    );

    assert_eq!(
        parse_command_from_input("/run ls -la"),
        ParsedCommand::Run("ls -la".to_string())
    );
    assert_eq!(
        parse_command_from_input("/run"),
        ParsedCommand::Unknown("/run: missing argument".to_string())
    );

    assert_eq!(
        parse_command_from_input("/drop bar.txt"),
        ParsedCommand::Drop("bar.txt".to_string())
    );
    assert_eq!(
        parse_command_from_input("/drop"),
        ParsedCommand::Unknown("/drop: missing argument".to_string())
    );

    assert_eq!(parse_command_from_input("/drop-all"), ParsedCommand::DropAll);
    assert_eq!(
        parse_command_from_input("/drop-and-clear-all"),
        ParsedCommand::DropAndClearAll
    );
    assert_eq!(parse_command_from_input("/help"), ParsedCommand::Help);
    assert_eq!(
        parse_command_from_input("/clear-logs"),
        ParsedCommand::ClearLogs
    );
    assert_eq!(
        parse_command_from_input("/clear-all"),
        ParsedCommand::ClearAll
    );
    assert_eq!(
        parse_command_from_input("/clear-task-history"),
        ParsedCommand::ClearTaskHistory
    );
    assert_eq!(
        parse_command_from_input("/max-task-retries"),
        ParsedCommand::MaxTaskRetriesShow
    );
    assert_eq!(
        parse_command_from_input("/max-task-retries-set 5"),
        ParsedCommand::MaxTaskRetriesSet("5".to_string())
    );
    assert_eq!(
        parse_command_from_input("/max-task-retries-set"),
        ParsedCommand::Unknown("/max-task-retries-set: missing argument".to_string())
    );

    assert_eq!(
        parse_command_from_input("/default-model-set my-alias"),
        ParsedCommand::DefaultModelSet("my-alias".to_string())
    );
    assert_eq!(
        parse_command_from_input("/default-model-set"),
        ParsedCommand::Unknown("/default-model-set: missing argument".to_string())
    );
    assert_eq!(
        parse_command_from_input("/default-model"),
        ParsedCommand::DefaultModelShow
    );

    assert_eq!(
        parse_command_from_input("  /foo bar"),
        ParsedCommand::Unknown("/foo".to_string())
    );
    assert_eq!(
        parse_command_from_input("hello world"),
        ParsedCommand::NotACommand
    );
    assert_eq!(
        parse_command_from_input(" /no lead slash"),
        ParsedCommand::Unknown("/no".to_string())
    );
    assert_eq!(parse_command_from_input(""), ParsedCommand::NotACommand);
    assert_eq!(parse_command_from_input("/"), ParsedCommand::NotACommand);

    assert_eq!(
        parse_command_from_input("/auto-test-command cargo build"),
        ParsedCommand::AutoTestCommand("cargo build".to_string())
    );
    assert_eq!(
        parse_command_from_input("/auto-test-command"),
        ParsedCommand::Unknown("/auto-test-command: missing argument".to_string())
    );
    assert_eq!(
        parse_command_from_input("/auto-test-toggle"),
        ParsedCommand::AutoTestToggle
    );

    assert_eq!(
        parse_command_from_input("/notification-command notify-send"),
        ParsedCommand::NotificationCommand("notify-send".to_string())
    );
    assert_eq!(
        parse_command_from_input("/notification-command"),
        ParsedCommand::Unknown("/notification-command: missing argument".to_string())
    );

    assert_eq!(
        parse_command_from_input("/research find all users"),
        ParsedCommand::Research("find all users".to_string())
    );
    assert_eq!(
        parse_command_from_input("/research"),
        ParsedCommand::Unknown("/research: missing argument".to_string())
    );
    assert_eq!(
        parse_command_from_input("/r find all users"),
        ParsedCommand::Research("find all users".to_string())
    );
    assert_eq!(
        parse_command_from_input("/r"),
        ParsedCommand::Unknown("/r: missing argument".to_string())
    );

    assert_eq!(
        parse_command_from_input("/ask what is love"),
        ParsedCommand::Ask("what is love".to_string())
    );
    assert_eq!(
        parse_command_from_input("/ask"),
        ParsedCommand::Unknown("/ask: missing argument".to_string())
    );

    assert_eq!(
        parse_command_from_input("/ask-add-task-info what is love"),
        ParsedCommand::AskAddTaskInfo("what is love".to_string())
    );
    assert_eq!(
        parse_command_from_input("/ask-add-task-info"),
        ParsedCommand::Unknown("/ask-add-task-info: missing argument".to_string())
    );

    assert_eq!(
        parse_command_from_input("/ask-no-context what is love"),
        ParsedCommand::AskNoContext("what is love".to_string())
    );
    assert_eq!(
        parse_command_from_input("/ask-no-context"),
        ParsedCommand::Unknown("/ask-no-context: missing argument".to_string())
    );

    assert_eq!(
        parse_command_from_input("/ask-no-context-add-task-info what is love"),
        ParsedCommand::AskNoContextAddTaskInfo("what is love".to_string())
    );
    assert_eq!(
        parse_command_from_input("/ask-no-context-add-task-info"),
        ParsedCommand::Unknown("/ask-no-context-add-task-info: missing argument".to_string())
    );

    assert_eq!(
        parse_command_from_input("/expert-model-set my-expert-alias"),
        ParsedCommand::ExpertModelSet("my-expert-alias".to_string())
    );
    assert_eq!(
        parse_command_from_input("/expert-model-set"),
        ParsedCommand::Unknown("/expert-model-set: missing argument".to_string())
    );
    assert_eq!(
        parse_command_from_input("/expert-model"),
        ParsedCommand::ExpertModelShow
    );
    assert_eq!(
        parse_command_from_input("/auto-expert-mode planning"),
        ParsedCommand::AutoExpertMode("planning".to_string())
    );
    assert_eq!(
        parse_command_from_input("/auto-expert-mode"),
        ParsedCommand::Unknown("/auto-expert-mode: missing argument".to_string())
    );

    assert_eq!(
        parse_command_from_input("/summary-model"),
        ParsedCommand::SummaryModelShow
    );
    assert_eq!(
        parse_command_from_input("/summary-model-set alias"),
        ParsedCommand::SummaryModelSet("alias".to_string())
    );
    assert_eq!(
        parse_command_from_input("/retry-model"),
        ParsedCommand::RetryModelShow
    );
    assert_eq!(
        parse_command_from_input("/retry-model-set alias"),
        ParsedCommand::RetryModelSet("alias".to_string())
    );
    assert_eq!(
        parse_command_from_input("/research-model"),
        ParsedCommand::ResearchModelShow
    );
    assert_eq!(
        parse_command_from_input("/research-model-set alias"),
        ParsedCommand::ResearchModelSet("alias".to_string())
    );
    assert_eq!(
        parse_command_from_input("/decision-model"),
        ParsedCommand::DecisionModelShow
    );
    assert_eq!(
        parse_command_from_input("/decision-model-set alias"),
        ParsedCommand::DecisionModelSet("alias".to_string())
    );
}
#[tokio::test]
async fn test_handle_command_exit() {
    let mut app = new_empty_app();
    let (tx, _rx) = mpsc::channel(1);

    app.input = "/exit".to_string();
    assert!(handle_command_if_present(&mut app, &tx).await);
    assert!(app.should_quit);
    assert!(app.input.is_empty());
}

#[tokio::test]
async fn test_handle_command_unknown_and_missing_args() {
    let mut app = new_empty_app();
    let (tx, _rx) = mpsc::channel(1);

    app.input = "/foo".to_string();
    assert!(handle_command_if_present(&mut app, &tx).await);
    assert!(!app.should_quit);
    assert!(app.input.is_empty());

    app.input = "/add".to_string();
    assert!(handle_command_if_present(&mut app, &tx).await);
    assert!(!app.should_quit);
    assert!(app.input.is_empty());
}

#[tokio::test]
async fn test_handle_command_not_a_command() {
    let mut app = new_empty_app();
    let (tx, _rx) = mpsc::channel(1);
    app.input = "send this to llm".to_string();
    assert!(!handle_command_if_present(&mut app, &tx).await);
    assert!(!app.should_quit);
    assert_eq!(app.input, "send this to llm");
}

#[tokio::test]
async fn test_handle_command_model_provider_url() {
    let mut app = new_empty_app();
    let (tx, _rx) = mpsc::channel(1);

    app.app_config.models_list = Some(vec![
        crate::config::file_config::ModelConfigEntry {
            alias: "test-alias".to_string(),
            model: "test-model-from-alias".to_string(),
            provider: "test-provider-from-alias".to_string(),
            provider_url: Some("http://alias.example.com".to_string()),
            provider_api_key: None,
        },
        crate::config::file_config::ModelConfigEntry {
            alias: "another-alias".to_string(),
            model: "another-model".to_string(),
            provider: "another-provider".to_string(),
            provider_url: None,
            provider_api_key: None,
        },
    ]);
    app.app_config.default_model = "another-alias".to_string();
    app.app_config.model = "another-model".to_string();
    app.app_config.provider = "another-provider".to_string();
    app.app_config.provider_url = "".to_string();

    app.input = "/default-model-set test-alias".to_string();
    assert!(handle_command_if_present(&mut app, &tx).await);
    assert_eq!(app.app_config.default_model, "test-alias");
    assert_eq!(app.app_config.model, "test-model-from-alias");
    assert_eq!(app.app_config.provider, "test-provider-from-alias");
    assert_eq!(app.app_config.provider_url, "http://alias.example.com/");
    assert!(app.input.is_empty());

    app.input = "/default-model-set unknown-alias".to_string();
    assert!(handle_command_if_present(&mut app, &tx).await);
    assert_eq!(app.app_config.default_model, "test-alias");
    assert!(app.input.is_empty());

    app.input = "/default-model".to_string();
    assert!(handle_command_if_present(&mut app, &tx).await);
    assert!(app.input.is_empty());
}

#[tokio::test]
async fn test_handle_command_add_drop_files() {
    let mut app = new_empty_app();
    let (tx, _rx) = mpsc::channel(1);

    let mut temp_file1 = NamedTempFile::new().unwrap();
    writeln!(temp_file1, "Content file 1").unwrap();
    let temp_file1_path_str = temp_file1.path().to_str().unwrap().to_string();
    let canonical_temp_path1 = tokio::fs::canonicalize(temp_file1.path()).await.unwrap();

    let mut temp_file2 = NamedTempFile::new().unwrap();
    writeln!(temp_file2, "Content file 2").unwrap();
    let temp_file2_path_str = temp_file2.path().to_str().unwrap().to_string();
    let canonical_temp_path2 = tokio::fs::canonicalize(temp_file2.path()).await.unwrap();

    let mut temp_file3 = NamedTempFile::new().unwrap();
    writeln!(temp_file3, "Content file 3").unwrap();
    let temp_file3_path_str = temp_file3.path().to_str().unwrap().to_string();
    let canonical_temp_path3 = tokio::fs::canonicalize(temp_file3.path()).await.unwrap();

    app.input = format!("/add {} {}", temp_file1_path_str, temp_file2_path_str);
    assert!(handle_command_if_present(&mut app, &tx).await);
    let files_map = app.ordered_files.get_all_labeled_files_map();
    assert!(files_map.contains_key(&canonical_temp_path1));
    assert!(files_map.contains_key(&canonical_temp_path2));
    assert_eq!(app.ordered_files.len(), 2);
    assert!(app.input.is_empty());

    app.input = format!("/add {}", temp_file3_path_str);
    assert!(handle_command_if_present(&mut app, &tx).await);
    assert_eq!(app.ordered_files.len(), 3);
    assert!(app.input.is_empty());

    app.input = format!("/drop {} {}", temp_file1_path_str, temp_file3_path_str);
    assert!(handle_command_if_present(&mut app, &tx).await);
    let files_map_after_drop = app.ordered_files.get_all_labeled_files_map();
    assert!(!files_map_after_drop.contains_key(&canonical_temp_path1));
    assert!(files_map_after_drop.contains_key(&canonical_temp_path2));
    assert!(!files_map_after_drop.contains_key(&canonical_temp_path3));
    assert_eq!(app.ordered_files.len(), 1);
    assert!(app.input.is_empty());

    let non_existent_file = "non_existent_file.txt";
    app.input = format!("/drop {} {}", non_existent_file, temp_file2_path_str);
    assert!(handle_command_if_present(&mut app, &tx).await);
    assert!(app.ordered_files.is_empty());
    assert!(app.input.is_empty());

    app.input = format!("/add {}", temp_file1_path_str);
    handle_command_if_present(&mut app, &tx).await;
    assert_eq!(app.ordered_files.len(), 1);

    app.input = "/drop-all".to_string();
    assert!(handle_command_if_present(&mut app, &tx).await);
    assert!(app.ordered_files.is_empty());
    assert!(app.input.is_empty());
}

#[tokio::test]
async fn test_handle_command_max_task_retries() {
    let mut app = new_empty_app();
    let (tx, _rx) = mpsc::channel(1);

    app.app_config.max_task_retries = 5;
    app.input = "/max-task-retries".to_string();
    assert!(handle_command_if_present(&mut app, &tx).await);
    assert!(app.input.is_empty());

    app.input = "/max-task-retries-set 10".to_string();
    assert!(handle_command_if_present(&mut app, &tx).await);
    assert_eq!(app.app_config.max_task_retries, 10);
    assert!(app.input.is_empty());

    app.input = "/max-task-retries-set foo".to_string();
    assert!(handle_command_if_present(&mut app, &tx).await);
    assert_eq!(app.app_config.max_task_retries, 10);
    assert!(app.input.is_empty());
}

#[tokio::test]
async fn test_handle_command_clear_task_history() {
    let mut app = new_empty_app();
    let (tx, _rx) = mpsc::channel(1);

    app.tasks.push(Task::new("test task".to_string(), None));
    app.last_completed_task = Some(Task::new("last task".to_string(), None));
    app.pruned_tasks_summary_messages.push(ChatMessage {
        role: crate::llm::ChatRole::System,
        content: "summary".to_string(),
        message_type: MessageType::Text,
    });

    assert!(!app.tasks.is_empty());
    assert!(app.last_completed_task.is_some());
    assert!(!app.pruned_tasks_summary_messages.is_empty());

    app.input = "/clear-task-history".to_string();
    assert!(handle_command_if_present(&mut app, &tx).await);

    assert!(app.tasks.is_empty());
    assert!(app.last_completed_task.is_none());
    assert!(app.pruned_tasks_summary_messages.is_empty());
    assert!(app.input.is_empty());
}

#[tokio::test]
async fn test_handle_command_clear_all() {
    let mut app = new_empty_app();
    let (tx, _rx) = mpsc::channel(1);

    app.app_config.task_info = Some("some info".to_string());
    app.tasks.push(Task::new("test task".to_string(), None));
    app.last_completed_task = Some(Task::new("last task".to_string(), None));
    app.pruned_tasks_summary_messages.push(ChatMessage {
        role: crate::llm::ChatRole::System,
        content: "summary".to_string(),
        message_type: MessageType::Text,
    });

    assert!(app.app_config.task_info.is_some());
    assert!(!app.tasks.is_empty());
    assert!(app.last_completed_task.is_some());
    assert!(!app.pruned_tasks_summary_messages.is_empty());

    app.input = "/clear-all".to_string();
    assert!(handle_command_if_present(&mut app, &tx).await);

    assert!(app.app_config.task_info.is_none());
    assert!(app.tasks.is_empty());
    assert!(app.last_completed_task.is_none());
    assert!(app.pruned_tasks_summary_messages.is_empty());
    assert!(app.input.is_empty());
}

#[tokio::test]
async fn test_handle_command_drop_and_clear_all() {
    let mut app = new_empty_app();
    let (tx, _rx) = mpsc::channel(1);

    app.app_config.task_info = Some("some info".to_string());
    app.tasks.push(Task::new("test task".to_string(), None));
    let mut temp_file = NamedTempFile::new().unwrap();
    writeln!(temp_file, "content").unwrap();
    app.ordered_files
        .add_path_recursively(&temp_file.path().to_path_buf())
        .await
        .unwrap();

    assert!(app.app_config.task_info.is_some());
    assert!(!app.tasks.is_empty());
    assert!(!app.ordered_files.is_empty());

    app.input = "/drop-and-clear-all".to_string();
    assert!(handle_command_if_present(&mut app, &tx).await);

    assert!(app.app_config.task_info.is_none());
    assert!(app.tasks.is_empty());
    assert!(app.ordered_files.is_empty());
    assert!(app.input.is_empty());
}
