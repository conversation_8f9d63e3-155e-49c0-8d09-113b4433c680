use directories::UserDirs;
use log::{debug, error, info, warn}; // Added debug
use serde::{Deserialize, Serialize};
use std::io::Write;
use std::path::PathBuf;
use std::{env, fs}; // Added env

pub const DEFAULT_CONFIG_FILENAME: &str = ".lledit.config.yml";

// Helper for deserializing provider_api_key, treating "none" as None
fn deserialize_optional_string_treat_none_as_null<'de, D>(
    deserializer: D,
) -> Result<Option<String>, D::Error>
where
    D: serde::Deserializer<'de>,
{
    let s: Option<String> = Option::deserialize(deserializer)?;
    match s {
        Some(val) if val.to_lowercase() == "none" => Ok(None),
        other => Ok(other),
    }
}

#[derive(Serialize, Deserialize, Debug, Clone)] // Removed Default here, will add to FileConfig
#[serde(deny_unknown_fields)]
pub struct ModelConfigEntry {
    pub alias: String,
    pub model: String,
    pub provider: String,
    #[serde(default, skip_serializing_if = "Option::is_none")]
    pub provider_url: Option<String>, // URL is optional, defaults to provider's default
    #[serde(
        default,
        skip_serializing_if = "Option::is_none",
        deserialize_with = "deserialize_optional_string_treat_none_as_null"
    )]
    pub provider_api_key: Option<String>, // Optional API key specific to this model entry. "none" is treated as null.
}

#[derive(Serialize, Deserialize, Debug, Default, Clone)]
#[serde(deny_unknown_fields)]
pub struct FileConfig {
    // LLM Provider settings
    // pub provider_api_key: Option<String>, // Removed global API key from file config
    // pub provider_url: Option<String>, // Removed
    // pub provider: Option<String>, // Removed
    // pub model: Option<String>, // Removed
    pub models_list: Option<Vec<ModelConfigEntry>>,
    pub default_model: Option<String>,

    // Results input (replaces message history)
    pub results_input_file: Option<PathBuf>,

    // Task & Prompting
    pub task_info: Option<String>,
    pub no_think: Option<Vec<String>>, // Changed to Vec<String> for YAML list

    // Output
    // pub results_output: Option<bool>, // Removed
    pub results_output_mode: Option<String>, // Added
    pub results_output_file: Option<PathBuf>,

    pub http_api_port: Option<u16>,

    // Other
    pub log_level: Option<u8>,
    // interactive: Option<bool>, // Removed
    pub exit_on_success: Option<bool>, // Added
    pub timestamps: Option<bool>,      // Added timestamps
    pub restore_previous_session_on_startup: Option<bool>, // Added
    pub restore_previous_session_models: Option<bool>, // Added

    // Auto-test feature
    pub auto_test_command: Option<String>,
    pub auto_test_toggle: Option<bool>, // This is for the boolean on/off for tests
    pub max_task_retries: Option<usize>,

    // Auto-research feature
    pub auto_research: Option<String>, // "false", "true", "forced", "first", "first-forced-then-false", "first-forced-then-true", "only-expert"

    // Auto-expert feature
    pub auto_expert: Option<String>, // "false", "true", "forced", "first", "first-forced-then-false", "first-forced-then-true"
    pub expert_model: Option<String>, // model alias
    pub auto_expert_mode: Option<String>, // "planning", "editing"
    pub expert_model_auto_research_loop_max: Option<usize>, // Added

    // Notification command
    pub notification_command: Option<String>,

    // General research loop settings
    pub decision_model_auto_research_loop_max: Option<usize>, // Added

    // Retry model for block parsing failures
    pub retry_model: Option<String>, // Alias of the model to use for retries

    // Summary model settings
    pub summary_model: Option<String>, // Alias of the model to use for summaries

    // Research model settings
    pub research_model: Option<String>, // Alias of the model to use for research tasks

    // Decision model settings
    pub decision_model: Option<String>, // Alias of the model to use for decision making

    // Advanced Language Features
    // Use a Vec<String> for simple YAML list representation.
    #[serde(default)]
    pub advanced_language_features: Vec<String>,
}

// Helper function to merge two FileConfig instances
// `overlay` takes precedence over `base`.
fn merge_file_configs(base: &mut FileConfig, overlay: FileConfig) {
    // if overlay.provider_api_key.is_some() { // Removed global API key
    //     base.provider_api_key = overlay.provider_api_key;
    // }
    if overlay.models_list.is_some() {
        base.models_list = overlay.models_list;
    }
    if overlay.default_model.is_some() {
        base.default_model = overlay.default_model;
    }
    if overlay.results_input_file.is_some() {
        base.results_input_file = overlay.results_input_file;
    } // Changed from message_history_file
    if overlay.task_info.is_some() {
        base.task_info = overlay.task_info;
    }
    if overlay.no_think.is_some() {
        base.no_think = overlay.no_think;
    }
    // if overlay.results_output.is_some() { base.results_output = overlay.results_output; } // Removed
    if overlay.results_output_mode.is_some() {
        base.results_output_mode = overlay.results_output_mode;
    } // Added
    if overlay.results_output_file.is_some() {
        base.results_output_file = overlay.results_output_file;
    }
    if overlay.http_api_port.is_some() {
        base.http_api_port = overlay.http_api_port;
    }
    if overlay.log_level.is_some() {
        base.log_level = overlay.log_level;
    }
    // if overlay.interactive.is_some() { base.interactive = overlay.interactive; } // Removed
    if overlay.exit_on_success.is_some() {
        base.exit_on_success = overlay.exit_on_success;
    } // Added
    if overlay.timestamps.is_some() {
        base.timestamps = overlay.timestamps;
    }
    if overlay.restore_previous_session_on_startup.is_some() {
        base.restore_previous_session_on_startup = overlay.restore_previous_session_on_startup;
    }
    if overlay.auto_test_command.is_some() {
        base.auto_test_command = overlay.auto_test_command;
    }
    if overlay.auto_test_toggle.is_some() {
        base.auto_test_toggle = overlay.auto_test_toggle;
    } // This is for the boolean on/off for tests
    if overlay.max_task_retries.is_some() {
        base.max_task_retries = overlay.max_task_retries;
    }
    if overlay.auto_research.is_some() {
        base.auto_research = overlay.auto_research;
    }
    if overlay.auto_expert.is_some() {
        base.auto_expert = overlay.auto_expert;
    }
    if overlay.expert_model.is_some() {
        base.expert_model = overlay.expert_model;
    }
    if overlay.auto_expert_mode.is_some() {
        base.auto_expert_mode = overlay.auto_expert_mode;
    }
    if overlay.expert_model_auto_research_loop_max.is_some() {
        // Added
        base.expert_model_auto_research_loop_max = overlay.expert_model_auto_research_loop_max;
    }
    if overlay.notification_command.is_some() {
        base.notification_command = overlay.notification_command;
    }
    if overlay.decision_model_auto_research_loop_max.is_some() {
        // Added
        base.decision_model_auto_research_loop_max = overlay.decision_model_auto_research_loop_max;
    }
    if overlay.retry_model.is_some() {
        base.retry_model = overlay.retry_model;
    }
    if overlay.summary_model.is_some() {
        base.summary_model = overlay.summary_model;
    }
    if overlay.research_model.is_some() {
        base.research_model = overlay.research_model;
    }
    if overlay.decision_model.is_some() {
        base.decision_model = overlay.decision_model;
    }
    if !overlay.advanced_language_features.is_empty() {
        base.advanced_language_features = overlay.advanced_language_features;
    }
}

fn get_home_config_path() -> Result<PathBuf, String> {
    UserDirs::new()
        .map(|ud| ud.home_dir().join(DEFAULT_CONFIG_FILENAME))
        .ok_or_else(|| "Could not determine user's home directory.".to_string())
}

fn get_local_config_path() -> Result<PathBuf, String> {
    env::current_dir()
        .map(|cd| cd.join(DEFAULT_CONFIG_FILENAME))
        .map_err(|e| format!("Could not determine current working directory: {}", e))
}

fn load_config_from_path(path: &PathBuf) -> Result<Option<FileConfig>, String> {
    if !path.exists() {
        return Ok(None);
    }
    match fs::read_to_string(path) {
        Ok(content) => {
            if content.trim().is_empty() {
                warn!(
                    "Config file at {} is empty. Treating as no config.",
                    path.display()
                );
                return Ok(None); // Treat empty file as if it wasn't there for merging purposes
            }
            serde_yaml::from_str(&content).map(Some).map_err(|e| {
                format!(
                    "Failed to parse configuration file at {}: {}. Please check its syntax.",
                    path.display(),
                    e
                )
            })
        }
        Err(e) => Err(format!(
            "Failed to read configuration file at {}: {}",
            path.display(),
            e
        )),
    }
}

fn create_default_config_file(path: &PathBuf) -> Result<(), String> {
    let default_content = r#"# LLEdit Configuration File
#
# This file allows you to set default values for LLEdit's command-line options.
# Settings in this file are overridden by command-line flags.
# Environment variables (like OPENAI_API_KEY) are overridden by settings here and by CLI flags.

# --- LLM Provider Settings ---

# API Keys are now configured per model in the `models_list` below using `provider_api_key`.
# A global API key can also be provided via the `--provider-api-key` CLI flag,
# which acts as a fallback if a specific model entry doesn't have its own key.

# Default model alias to use from the models_list below.
# This alias must match one of the aliases defined in the models_list.
# Example: "local-qwen3-32b"
# default_model: "local-qwen3-32b"

# Model alias (from models_list) to use for expert tasks.
# If not set, and auto_expert is 'true' or 'forced', an error will occur.
# Example: "cloud-openai-gpt4o"
# expert_model: "cloud-openai-gpt4o"

# List of available model configurations.
# Each entry requires an 'alias', 'provider', and 'model'.
# 'provider_url' is optional and will override the default for that provider if set.
# 'provider_api_key' is optional. If provided, it overrides any CLI global API key or environment variable for this specific model.
# Setting provider_api_key to "none" (case-insensitive string) is equivalent to it being absent or null.
# models_list:
#   - alias: "local-qwen3-32b" # Example for a locally hosted Qwen model via Ollama
#     provider: "ollama" # Use 'ollama' provider for GenAI's Ollama integration
#     model: "qwen/qwen2:72b-instruct-q4_K_M" # Example model name for Ollama
#     provider_url: "http://localhost:11434" # Ollama typically runs here, GenAI expects host:port
#     # provider_api_key: "none" # Ollama usually doesn't require an API key
#
#   - alias: "cloud-openai-gpt4o" # Example for a cloud-hosted OpenAI model
#     provider: "openai" # Use 'openai' provider for GenAI's OpenAI integration
#     model: "gpt-4o"
#     # provider_url: "https://api.openai.com/v1" # Optional: GenAI's default for OpenAI is usually fine
#     # provider_api_key: "sk-yourOpenAIKeyHere" # Can be set per model, overrides CLI global/env
#
#   - alias: "openrouter-mistral-large" # Example for OpenRouter
#     provider: "openrouter"
#     model: "mistralai/mistral-large-latest" # Model identifier as used by OpenRouter
#     # provider_url: "https://openrouter.ai/api/v1" # This is fixed for OpenRouter and will be ignored if set
#     provider_api_key: "sk-or-yourOpenRouterApiKeyHere" # Specific API key for OpenRouter

# --- Results Input (replaces Message History) ---

# Path to a JSON file containing results data from a previous run to load as initial state.
# Example: "/path/to/your/previous_results.json"
# results_input_file: null

# --- Task & Prompting ---

# Additional detailed information about the task to be provided at the start of the initial prompt.
# Example: "Always respond in a formal tone."
# task_info: null

# Set the 'no_think' modes. This is a list of modes.
# If a mode is in this list, prompts for that context get a /no_think prefix.
# Otherwise, they get a /think prefix (unless 'none' or 'all' changes this).
# Valid modes:
# - "none": Disables /no_think and /think prefixes entirely for all prompts. Overrides all other modes.
# - "all": Adds /no_think prefix to all LLM prompts (unless "none" is also present).
# - "editing-planning" # For the initial prompt where the LLM plans code edits.
# - "editing-coding" # For prompts where the LLM generates specific code replacements.
# - "research-auto-decision" # For the prompt where the LLM decides if automated research is needed.
# - "research-planning" # For the initial prompt when planning a research task.
# - "research-searching" # For subsequent prompts during a research cycle (e.g., interpreting search results).
# - "question" # For general question/answer prompts.
# - "summaries" # For prompts requesting summaries (e.g., plan summary, task summary).
# - "expert-planning" # For the expert model's planning prompt.
# - "expert-editing" # For the expert model's editing prompt.
# Example: ["editing-planning", "research-planning", "expert-planning"]
# Default: [] (empty list, meaning /think prefix is used by default for all contexts if not 'none' or 'all')
# no_think: []

# --- Output ---

# Set the results output mode to stdout.
# Options: "none", "simple", "advanced".
# If results_output_file is set, this mode is ignored for file output (which is always advanced JSON).
# Default: "none"
# results_output_mode: "none"

# Path to file to output JSON results (always in 'advanced' JSON format).
# Example: "/path/to/your/results.json"
# results_output_file: null

# --- Other Settings ---

# Set the logging level.
# 1 = Limited logs.
# 2 = Default logging that provides a solid overview of what is happening and reads well.
# 3 = Extremely verbose logging, including raw LLM responses.
# Default: 2
# log_level: 2

# --- Interactive Mode & Exit Behavior ---

# Exit after the first successful task completion (including auto-tests).
# Default: false
# exit_on_success: false

# Enable or disable timestamps in logs.
# Default: true (timestamps are enabled)
# timestamps: true
    # timestamps: true
    
    # If true, saves the current session (tasks, file context, etc.) to `.lledit.previous_session`
    # on exit and restores it on the next startup.
    # Default: false
    # restore_previous_session_on_startup: false

    # --- Auto-test Settings ---

# Command to run automatically after edits are applied.
# Example: "cargo test" or "npm run test"
# auto_test_command: null

# Enable the auto-test feature by default (true/false). This is for the test execution itself.
# Default: false
# auto_test_toggle: false

# Maximum number of times a task can be retried due to auto-test failures.
# Default: 99999
# max_task_retries: 99999

# --- Auto-Research Settings ---
# Controls the automatic research behavior when starting an editing task.
# Options: "false", "true", "forced", "first", "first-forced-then-false", "first-forced-then-true", "only-expert"
# - "false": Auto-research is disabled.
# - "true": (Default) Auto-research is mandatory if no files are in context. Otherwise, the LLM decides.
# - "forced": Auto-research is always mandatory.
# - "first": Like "true", but only on the first run of an editing task (not on auto-test retries).
# - "first-forced-then-false": Like "forced" on the first run, then "false" on retries.
# - "first-forced-then-true": Like "forced" on the first run, then "true" on retries.
# - "only-expert": The default LLM will not be prompted to decide on or initiate research. Research can only be triggered by the expert model if expert_model_auto_research_loop_max > 0.
# Default: "true"
# auto_research: "true"

# --- Auto-Expert Settings ---
# Controls the automatic expert AI functionality.
# Options: "false", "true", "forced", "first", "first-forced-then-false", "first-forced-then-true"
# - "false": (Default) Auto-expert is disabled.
# - "true": The default LLM decides if the task is complex enough to warrant an expert.
# - "forced": Auto-expert flow is always triggered for the specified mode.
# - "first": Like "true", but only on the first run of an editing task.
# - "first-forced-then-false": Like "forced" on the first run, then "false" on retries.
# - "first-forced-then-true": Like "forced" on the first run, then "true" on retries.
# auto_expert: "false"

# Mode for auto-expert.
# Options: "planning", "editing"
# - "planning": (Default) Expert LLM handles the initial planning phase.
# - "editing": Expert LLM handles individual code generation tasks (not yet implemented).
# auto_expert_mode: "planning"

# Maximum number of times the expert LLM can request research within a single edit cycle.
# Default: 2
# expert_model_auto_research_loop_max: 2

# --- Notification Settings ---

# Command to run for notifications. LLEdit will set LLEDIT_NOTIFICATION_TITLE and LLEDIT_NOTIFICATION_CONTENTS environment variables.
# Example for macOS: "osascript -e 'display notification \"${LLEDIT_NOTIFICATION_CONTENTS}\" with title \"${LLEDIT_NOTIFICATION_TITLE}\"'"
# Example for Linux (notify-send): "notify-send \"${LLEDIT_NOTIFICATION_TITLE}\" \"${LLEDIT_NOTIFICATION_CONTENTS}\""
# notification_command: null

# --- General Research Loop Settings ---

# Maximum number of times the default LLM (decision model) can request further research
# after an auto-research phase has completed within a single edit cycle.
# Default: 2
# decision_model_auto_research_loop_max: 2

# --- General Research Loop Settings ---

# Maximum number of times the default LLM (decision model) can request further research
# after an auto-research phase has completed within a single edit cycle.
# Default: 2
# decision_model_auto_research_loop_max: 2

# --- Retry Model Settings ---

# Model alias (from models_list) to use for retrying block parsing failures.
# If set to "none", "default", or not set, the model that made the original failing call will be used.
# Example: "cloud-openai-gpt35t" (a potentially faster/cheaper model for simple fixes)
# retry_model: null

# --- Summary Model Settings ---

# Model alias (from models_list) to use for generating all summaries.
# If set to "none", "default", or not set, the default model will be used.
# Example: "cloud-openai-gpt35t"
# summary_model: null

# --- Research Model Settings ---

# Model alias (from models_list) to use for research and auto-research tasks.
# If set to "none", "default", or not set, the main default model will be used.
# Using a different model here will mean research starts with a fresh context (no prior message history).
# Example: "cloud-openai-gpt35t" (a potentially faster/cheaper model for research)
# research_model: null

# --- Decision Model Settings ---

# Model alias (from models_list) to use for internal decision-making prompts
# (e.g., deciding if auto-research is needed, or if more research is needed after an auto-research phase).
# If set to "none", "default", or not set, the main default model will be used.
# Using a specific decision model here will provide it with a focused context (no files message).
# Example: "cloud-openai-gpt35t" (a potentially faster/cheaper model for decisions)
# decision_model: null

# --- Advanced Language Features ---

# Enable specific, language-aware features. This is a list of feature names.
# Example: ["rust-advanced-syntax-checking"]
#
# Available features:
# - "rust-advanced-syntax-checking": Before writing edits to a Rust file,
#   verifies that the resulting file is syntactically correct using `syn::parse_file`.
#   If parsing fails, it triggers a targeted LLM retry to fix the syntax errors.
#   If `syn::parse_file` fails with "cannot parse string into token stream",
#   a specialized retry prompt is used, focusing on fundamental issues like
#   delimiter mismatches or malformed tokens. For other `syn` errors, a general
#   syntax fix prompt is used. This helps prevent many common LLM errors.
# advanced_language_features: []
"#;
    match fs::File::create(path) {
        Ok(mut file) => {
            file.write_all(default_content.as_bytes())
                .map_err(|e| format!("Failed to write to default config file: {}", e))?;
            info!("Created default configuration file at: {}", path.display());
            Ok(())
        }
        Err(e) => Err(format!("Failed to create default config file: {}", e)),
    }
}

pub fn load_or_create_config() -> Result<FileConfig, String> {
    let mut final_config = FileConfig::default(); // Start with application defaults

    // 1. Load Home Config
    match get_home_config_path() {
        Ok(home_path) => {
            match load_config_from_path(&home_path) {
                Ok(Some(home_cfg)) => {
                    debug!("Loaded home config from: {}", home_path.display());
                    merge_file_configs(&mut final_config, home_cfg);
                }
                Ok(None) => {
                    info!(
                        "Home configuration file not found at {}. Creating a default one.",
                        home_path.display()
                    );
                    if let Err(e_create) = create_default_config_file(&home_path) {
                        warn!(
                            "Failed to create default home config: {}. Using application defaults.",
                            e_create
                        );
                    } else {
                        // Try loading again after creation
                        match load_config_from_path(&home_path) {
                            Ok(Some(created_home_cfg)) => {
                                debug!(
                                    "Loaded newly created home config from: {}",
                                    home_path.display()
                                );
                                merge_file_configs(&mut final_config, created_home_cfg);
                            }
                            Ok(None) => {
                                // Should not happen if creation succeeded and file is not empty
                                warn!("Newly created home config at {} is empty or unreadable. Using application defaults.", home_path.display());
                            }
                            Err(e_load_created) => {
                                // If the newly created default config is unparseable, it's a critical issue.
                                error!(
                                    "Failed to load newly created default home config at {}: {}.",
                                    home_path.display(),
                                    e_load_created
                                );
                                return Err(format!("Critical error: Default configuration template at {} is invalid: {}", home_path.display(), e_load_created));
                            }
                        }
                    }
                }
                Err(e) => {
                    // If an existing home config file is unreadable/unparseable, make it a fatal error.
                    error!(
                        "Error processing home config at {}: {}",
                        home_path.display(),
                        e
                    );
                    return Err(format!(
                        "Failed to load home configuration from {}: {}",
                        home_path.display(),
                        e
                    ));
                }
            }
        }
        Err(e) => {
            warn!(
                "Could not determine home config path: {}. Skipping home config.",
                e
            );
        }
    }

    // 2. Load Local (Working Directory) Config and merge/override
    match get_local_config_path() {
        Ok(local_path) => {
            if local_path.exists() {
                // Only attempt to load if it exists
                // If a local config file exists, check/update .gitignore
                if let Err(e) = ensure_config_in_gitignore(DEFAULT_CONFIG_FILENAME) {
                    warn!("Attempt to update .gitignore failed: {}", e);
                    // Continue regardless, this is not a critical failure for config loading.
                }

                match load_config_from_path(&local_path) {
                    Ok(Some(local_cfg)) => {
                        debug!("Loaded local config from: {}", local_path.display());
                        merge_file_configs(&mut final_config, local_cfg);
                        info!(
                            "Applied local config overrides from: {}",
                            local_path.display()
                        );
                    }
                    Ok(None) => {
                        // File exists but was empty, already warned by load_config_from_path
                        debug!(
                            "Local config file at {} is present but empty. No overrides applied.",
                            local_path.display()
                        );
                    }
                    Err(e) => {
                        // If an existing local config file is unreadable/unparseable, make it a fatal error.
                        error!(
                            "Error processing local config at {}: {}",
                            local_path.display(),
                            e
                        );
                        return Err(format!(
                            "Failed to load local configuration from {}: {}",
                            local_path.display(),
                            e
                        ));
                    }
                }
            } else {
                debug!(
                    "No local config file found at {}. No local overrides applied.",
                    local_path.display()
                );
            }
        }
        Err(e) => {
            warn!(
                "Could not determine local config path: {}. Skipping local config.",
                e
            );
        }
    }

    Ok(final_config)
}

// Ensures that the default config filename is in .gitignore if a local config and .gitignore exist
fn ensure_config_in_gitignore(config_filename: &str) -> Result<(), String> {
    let current_dir =
        env::current_dir().map_err(|e| format!("Failed to get current directory: {}", e))?;
    let gitignore_path = current_dir.join(".gitignore");

    if gitignore_path.exists() {
        debug!("Found .gitignore at {}", gitignore_path.display());
        let content = match fs::read_to_string(&gitignore_path) {
            Ok(c) => c,
            Err(e) => {
                // If .gitignore exists but is unreadable, we probably shouldn't try to write to it.
                warn!(
                    "Failed to read .gitignore at {}: {}. Skipping .gitignore update.",
                    gitignore_path.display(),
                    e
                );
                return Ok(()); // Not a critical error for the main config loading process
            }
        };

        let already_ignored = content.lines().any(|line| line.trim() == config_filename);

        if !already_ignored {
            info!(
                "Adding '{}' to .gitignore at {}",
                config_filename,
                gitignore_path.display()
            );
            let mut file = match fs::OpenOptions::new().append(true).open(&gitignore_path) {
                Ok(f) => f,
                Err(e) => {
                    warn!("Failed to open .gitignore for appending at {}: {}. Skipping .gitignore update.", gitignore_path.display(), e);
                    return Ok(()); // Not a critical error
                }
            };

            let mut new_entry = String::new();
            if !content.is_empty() && !content.ends_with('\n') {
                new_entry.push('\n');
            }
            new_entry.push_str(config_filename);
            new_entry.push('\n');

            if let Err(e) = file.write_all(new_entry.as_bytes()) {
                warn!(
                    "Failed to write to .gitignore at {}: {}. Skipping .gitignore update.",
                    gitignore_path.display(),
                    e
                );
                // Even if write fails, proceed with config loading
            }
        } else {
            debug!(
                "'{}' is already in .gitignore at {}",
                config_filename,
                gitignore_path.display()
            );
        }
    } else {
        debug!(
            ".gitignore not found in current directory ({}). Skipping check for '{}'.",
            gitignore_path.display(),
            config_filename
        );
    }
    Ok(())
}